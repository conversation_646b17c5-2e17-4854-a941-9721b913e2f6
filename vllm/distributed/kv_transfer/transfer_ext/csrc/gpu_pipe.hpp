#pragma once
#include "cpu_pipe.hpp"

namespace py = pybind11;

// 类型转换: torch 数据类型 -> nccl 传输类型
auto torch_dtype_to_nccl_dtype(torch::ScalarType dtype) -> ncclDataType_t {
  switch (dtype) {
    case torch::kFloat32:
      return ncclFloat32;
    case torch::kFloat64:
      return ncclFloat64;
    case torch::kFloat16:
      return ncclFloat16;
    case torch::kBFloat16:
      return ncclBfloat16;
    case torch::kInt8:
      return ncclInt8;
    case torch::kInt32:
      return ncclInt32;
    case torch::kInt64:
      return ncclInt64;
    case torch::kUInt8:
      return ncclUint8;
    case torch::kUInt32:
      return ncclUint32;
    case torch::kUInt64:
      return ncclUint64;
    default:
      throw std::runtime_error("Unsupported torch dtype for NCCL: " +
                               std::string(torch::toString(dtype)));
  }
}

auto torch_dtype_to_nccl_dtype(int dtype) -> ncclDataType_t {
  return torch_dtype_to_nccl_dtype(static_cast<torch::ScalarType>(dtype));
}

// 类型转换: 数据类型 ncclUniqueId <-> 对象类型 std::string
auto to_string(const ncclUniqueId& id) -> std::string {
  std::string id_str(reinterpret_cast<const char*>(&id), sizeof(ncclUniqueId));
  return id_str;
}
auto to_ncclUniqueId(const std::string& id_str) -> ncclUniqueId {
  if (id_str.size() != sizeof(ncclUniqueId)) {
    throw std::invalid_argument("Invalid ncclUniqueId size");
  }
  ncclUniqueId id;
  std::memcpy(&id, id_str.data(), sizeof(ncclUniqueId));
  return id;
}

// NcclComm 类: 封装 Tensor 的 NCCL 通信
class NcclComm {
 public:
  NcclComm(std::string host, int port, int rank, int size, int device) {
    // 完全从头构建
    this->rank_ = rank;
    this->size_ = size;
    this->device_ = device;

    // 单进程不需要 NCCL 通信
    if (this->size_ == 1) return;

    this->group_ =
        std::make_shared<StatelessProcessGroup>(host, port, rank, size);

    this->initialize_nccl();
  }

  NcclComm(std::shared_ptr<StatelessProcessGroup> group, int device) {
    // 从现有的 StatelessProcessGroup 构建
    this->rank_ = group->rank();
    this->size_ = group->size();
    this->device_ = device;
    this->group_ = group;

    // 单进程不需要 NCCL 通信
    if (this->size_ == 1) return;

    // 初始化 NCCL 通信
    this->initialize_nccl();
  }

  ~NcclComm() {
    if (this->comm_) ncclCommDestroy(this->comm_);
  }

  void send(const void* buf, size_t count, int dtype, int peer, void* stream) {
    if (!this->comm_) {
      throw std::runtime_error("NCCL communication not initialized");
    }

    // 调试输出: 发送地址和大小
    DEBUG_PRINTF(
        "[NcclComm] Rank %d sending to peer %d: buf=%p, count=%zu, type "
        "size=%d\n",
        this->rank_, peer, buf, count,
        ncclTypeSizeExt(torch_dtype_to_nccl_dtype(dtype)));

    ncclGroupStart();
    ncclSend(buf, count, torch_dtype_to_nccl_dtype(dtype), peer, this->comm_,
             (cudaStream_t)stream);
    ncclGroupEnd();
  }

  void recv(void* buf, size_t count, int dtype, int peer, void* stream) {
    if (!this->comm_) {
      throw std::runtime_error("NCCL communication not initialized");
    }

    // 调试输出: 接收地址和大小
    DEBUG_PRINTF(
        "[NcclComm] Rank %d receiving from peer %d: buf=%p, count=%zu, type "
        "size=%d\n",
        this->rank_, peer, buf, count,
        ncclTypeSizeExt(torch_dtype_to_nccl_dtype(dtype)));

    ncclGroupStart();
    ncclRecv(buf, count, torch_dtype_to_nccl_dtype(dtype), peer, this->comm_,
             (cudaStream_t)stream);
    ncclGroupEnd();
  }

  // python 可用的接口
  void py_send(const size_t buf, const size_t count, const int dtype,
               const int peer, size_t stream) {
    send((const void*)buf, count, dtype, peer, (void*)stream);
  }

  // python 可用的接口
  void py_recv(const size_t buf, const size_t count, const int dtype,
               const int peer, size_t stream) {
    recv((void*)buf, count, dtype, peer, (void*)stream);
  }

 private:
  auto initialize_nccl() -> void {
    // 广播 id_ 到所有进程
    if (this->rank_ == 0) {
      ncclGetUniqueId(&this->id_);
      this->group_->broadcast_obj(to_string(this->id_), 0);
    } else {
      std::string id_str = this->group_->broadcast_obj({}, 0);
      if (id_str.empty()) {
        throw std::runtime_error("Failed to receive ncclUniqueId from rank 0");
      }
      this->id_ = to_ncclUniqueId(id_str);
    }

    printf("[NcclComm] Rank %d, Size %d, UniqueId %x\n", this->rank_,
           this->size_, this->id_.internal);

    // 初始化 NCCL 通信
    cudaSetDevice(this->device_);  // 或根据实际映射关系设置设备
    ncclResult_t res =
        ncclCommInitRank(&this->comm_, this->size_, this->id_, this->rank_);
    if (res != ncclSuccess) throw std::runtime_error("NCCL init failed");

    printf("[NcclComm] NCCL communication initialized for rank %d\n",
           this->rank_);

    // 预热通信
    this->warmup_nccl();
  }

  auto warmup_nccl() -> void {
    auto warm_tensor =
        torch::empty({1024}, torch::kFloat32).to(torch::kCUDA, this->device_);
    // 预热所有的点对点通信
    for (int broadcast_rank = 0; broadcast_rank < this->size_;
         ++broadcast_rank) {
      if (broadcast_rank == this->rank_) {
        // 广播到其他进程
        for (int peer = 0; peer < this->size_; ++peer) {
          if (peer != broadcast_rank) {
            this->send(warm_tensor.data_ptr(), warm_tensor.numel(),
                       (int)torch::kFloat32, peer, nullptr);
          }
        }
      } else {
        // 从其他进程接收广播
        this->recv(warm_tensor.data_ptr(), warm_tensor.numel(),
                   (int)torch::kFloat32, broadcast_rank, nullptr);
      }
    }
    printf("[NcclComm] NCCL warmup completed for rank %d\n", this->rank_);
  }

 private:
  // 基本属性
  int rank_ = 0;
  int size_ = 0;
  int device_ = -1;
  std::shared_ptr<StatelessProcessGroup> group_ = nullptr;

  ncclComm_t comm_ = nullptr;
  ncclUniqueId id_;
};

// 模块封装代码
PYBIND11_MODULE(gpu_pipe, m) {
  py::class_<NcclComm, std::shared_ptr<NcclComm>>(m, "NcclComm")
      .def(py::init<std::string, int, int, int, int>(), py::arg("host"),
           py::arg("port"), py::arg("rank"), py::arg("size"), py::arg("device"))
      .def(py::init<std::shared_ptr<StatelessProcessGroup>, int>(),
           py::arg("group"), py::arg("device"))
      .def("py_send", &NcclComm::py_send, py::arg("buf"), py::arg("count"),
           py::arg("dtype"), py::arg("peer"), py::arg("stream"))
      .def("py_recv", &NcclComm::py_recv, py::arg("buf"), py::arg("count"),
           py::arg("dtype"), py::arg("peer"), py::arg("stream"));
}