#pragma once

#include "pipe.hpp"
#include "kv_placer_kernel.h"
#include "debug_utils.h"
#include <chrono>
#include <utility>  // for std::move
#include <cstdlib>  // for rand()

struct SendTask {
  std::string request_id;
  int recv_rank;
  torch::Tensor kv;
  std::chrono::high_resolution_clock::time_point enqueue_time;
};

struct RecvTask {
  std::string request_id;
  int send_rank;
};

struct PlacerInfo {
  torch::Tensor key_cache_layer_ptrs;    // key block table 层指针
  torch::Tensor value_cache_layer_ptrs;  // value block table 层指针
  int block_size;                        // block size
  int block_stride;                      // block stride
  std::string kv_cache_dtype;            // KV cache 数据类型
  double k_scale;                        // K 缩放因子
  double v_scale;                        // V 缩放因子
  int num_layers;                        // 模型层数
  int num_heads;                         // 模型头数
  int head_size;                         // 模型头大小
};

struct PlaceTask {
  // request info
  std::string request_id;
  torch::Tensor slot_mapping;
  torch::Tensor
      recv_tensor;  // [k/v, num_layers, num_tokens, num_heads, head_size]
  // placer info
  std::shared_ptr<PlacerInfo> placer_info;  // 放置信息
};

// 通信任务执行
class KvComm {
 public:
  constexpr static int NUM_PIPE_PER_RANK = 4;  // 每个 TP 一组, 一组 4 个.

  KvComm(std::string host, int port, int rank, int size, int tp_rank,
         int device, size_t recv_buffer_size_threshold) {
    this->rank_ = rank;
    this->size_ = size;
    this->device_ = device;

    // 检查参数合法性
    if (rank < 0 || rank >= size) {
      throw std::invalid_argument(
          "Rank must be in [0, size), but got rank=" + std::to_string(rank) +
          ", size=" + std::to_string(size));
    }
    cudaSetDevice(device);

    // 初始化 Pipe 对象
    int pipe_rank_offset =
        this->NUM_PIPE_PER_RANK * tp_rank;  // 每个 TP 一组, 一组 4 个.
    this->signal_pipe_ =
        std::make_shared<Pipe>(host, port + pipe_rank_offset + 0, rank, size,
                               device, recv_buffer_size_threshold);
    this->ack_pipe_ =
        std::make_shared<Pipe>(host, port + pipe_rank_offset + 1, rank, size,
                               device, recv_buffer_size_threshold);
    this->data_pipe_up_ =
        std::make_shared<Pipe>(host, port + pipe_rank_offset + 2, rank, size,
                               device, recv_buffer_size_threshold);
    this->data_pipe_down_ =
        std::make_shared<Pipe>(host, port + pipe_rank_offset + 3, rank, size,
                               device, recv_buffer_size_threshold);

    // 初始化 CUDA 流矩阵
    this->stream_matrix_.resize(size);
    for (int i = 0; i < size; ++i) {
      this->stream_matrix_[i].resize(size);
      for (int j = 0; j < size; ++j) {
        cudaStreamCreateWithFlags(&this->stream_matrix_[i][j],
                                  cudaStreamNonBlocking);
      }
    }
    // 初始化放置流
    cudaStreamCreateWithFlags(&this->placer_stream_, cudaStreamNonBlocking);
    printf("[KvComm] Initialized with rank %d, size %d, host %s, port %d\n",
           rank, size, host.c_str(), port + pipe_rank_offset);
  }

  ~KvComm() {
    // 清理 CUDA 流
    for (int i = 0; i < this->size_; ++i) {
      for (int j = 0; j < this->size_; ++j) {
        if (this->stream_matrix_[i][j] != nullptr) {
          cudaStreamDestroy(this->stream_matrix_[i][j]);
        }
      }
    }
    if (this->placer_stream_ != nullptr) {
      cudaStreamDestroy(this->placer_stream_);
    }
  }

  // 完成发送任务
  auto send(const SendTask& task) -> void {
    // DEBUG: Print tensor reference count at start of send
    printf(
        "[TENSOR_DEBUG] KVCOMM_SEND_START - Request: %s, Tensor ptr: %p, "
        "use_count: %ld, data_ptr: %p\n",
        task.request_id.c_str(), &task.kv, task.kv.use_count(),
        task.kv.data_ptr());

    // 检查任务参数
    if (!task.kv.defined()) {
      throw std::invalid_argument("Tensor is not defined in SendTask");
    }
    if (task.kv.device().type() != torch::kCUDA ||
        task.kv.device().index() != this->device_) {
      throw std::invalid_argument(
          "Tensor must be on the same CUDA device as the KvComm");
    }

    // 发送请求 ID 和张量
    this->signal_pipe_->send_obj(task.request_id, task.recv_rank);
    DEBUG_PRINTF("[KvComm] [SEND] Sending request ID %s to rank %d\n",
                 task.request_id.c_str(), task.recv_rank);

    // 确保接收端已准备好
    std::string ack_request_id = this->ack_pipe_->recv_obj(task.recv_rank);
    if (ack_request_id != task.request_id) {
      throw std::runtime_error("Ack request ID mismatch: " + ack_request_id +
                               " != " + task.request_id);
    }
    auto data_pipe = this->get_data_pipe(this->rank_, task.recv_rank);
    auto stream = this->get_stream(this->rank_, task.recv_rank);

    DEBUG_PRINTF(">>>>>>>>>>>>> SEND START %s to %d\n", task.request_id.c_str(),
                 task.recv_rank);

    // 调试输出: 发送地址和大小
    DEBUG_PRINTF(
        "[KvComm] Rank %d sending tensor to rank %d: buf=%p, count=%ld, "
        "torch type size=%ld\n",
        this->rank_, task.recv_rank, task.kv.data_ptr(), (long)task.kv.numel(),
        (long)task.kv.element_size());

    // DEBUG: Print tensor reference count before actual tensor send
    printf(
        "[TENSOR_DEBUG] BEFORE_TENSOR_SEND - Request: %s, Tensor ptr: %p, "
        "use_count: %ld, data_ptr: %p\n",
        task.request_id.c_str(), &task.kv, task.kv.use_count(),
        task.kv.data_ptr());

    // 发送张量
    data_pipe->send_tensor(task.kv, task.recv_rank, stream);

    // DEBUG: Print tensor reference count after tensor send
    printf(
        "[TENSOR_DEBUG] AFTER_TENSOR_SEND - Request: %s, Tensor ptr: %p, "
        "use_count: %ld, data_ptr: %p\n",
        task.request_id.c_str(), &task.kv, task.kv.use_count(),
        task.kv.data_ptr());

    DEBUG_PRINTF(">>>>>>>>>>>>> SEND END %s to %d\n", task.request_id.c_str(),
                 task.recv_rank);
  }

  auto poll() -> std::optional<RecvTask> {
    // 轮询信号管道, 检查是否有新的接收任务
    for (int i = 0; i < this->size_; ++i) {
      int poll_rank = this->poll_counter_++ % this->size_;
      if (poll_rank == this->rank_) continue;  // 跳过自己

      std::string request_id = this->signal_pipe_->poll_obj(poll_rank);
      if (!request_id.empty()) {
        DEBUG_PRINTF("[KvComm] [POLL] request ID %s from rank %d\n",
                     request_id.c_str(), poll_rank);
        return RecvTask{request_id, poll_rank};
      }
    }
    return {};  // 没有任务时返回空任务
  }

  // 接收指定的来源的 Tensor
  auto recv(const RecvTask& task) -> torch::Tensor {
    // 检查任务参数
    if (task.send_rank < 0 || task.send_rank >= this->size_) {
      throw std::out_of_range("Send rank out of range: " +
                              std::to_string(task.send_rank));
    }

    // 确认接收
    this->ack_pipe_->send_obj(task.request_id, task.send_rank);
    DEBUG_PRINTF("[KvComm] Acknowledged request ID %s from rank %d\n",
                 task.request_id.c_str(), task.send_rank);

    auto data_pipe = this->get_data_pipe(task.send_rank, this->rank_);
    auto stream = this->get_stream(task.send_rank, this->rank_);

    DEBUG_PRINTF("<<<<<<<<<<<<< RECV START %s to %d\n", task.request_id.c_str(),
                 task.send_rank);

    // 调试输出: 接收的请求 id
    DEBUG_PRINTF(
        "[KvComm] Rank %d receiving tensor from rank %d: request ID %s\n",
        this->rank_, task.send_rank, task.request_id.c_str());

    auto tensor = data_pipe->recv_tensor(task.send_rank, stream);
    DEBUG_PRINTF("<<<<<<<<<<<<< RECV END %s to %d\n", task.request_id.c_str(),
                 task.send_rank);
    return tensor;
  }

  // 将 Tensor 放置到 KV block table 中
  auto place(PlaceTask& task) -> void {
    DEBUG_PRINTF(
        "[KvComm] [PLACE_START] Starting placement for request ID %s\n",
        task.request_id.c_str());

    // 打印接收的 token 个数和 slot_mapping 中的 token 个数
    int64_t recv_tokens =
        task.recv_tensor.size(2);  // recv_tensor shape: [k/v, num_layers,
                                   // num_tokens, num_heads, head_size]
    int64_t slot_mapping_tokens =
        task.slot_mapping.size(0);  // slot_mapping shape: [num_tokens]
    DEBUG_PRINTF(
        "[KvComm] [TOKEN_COUNT] recv_tensor tokens: %ld, slot_mapping tokens: "
        "%ld\n",
        recv_tokens, slot_mapping_tokens);

    // 检查长度不一致并进行对齐
    if (recv_tokens != slot_mapping_tokens) {
      int64_t aligned_tokens = std::min(recv_tokens, slot_mapping_tokens);
      DEBUG_PRINTF(
          "[KvComm] [TOKEN_ALIGN] Length mismatch detected! recv_tokens=%ld, "
          "slot_mapping_tokens=%ld, aligning to smaller length=%ld\n",
          recv_tokens, slot_mapping_tokens, aligned_tokens);

      // 对 recv_tensor 进行切片对齐
      if (recv_tokens > aligned_tokens) {
        task.recv_tensor = task.recv_tensor.narrow(2, 0, aligned_tokens);
        DEBUG_PRINTF(
            "[KvComm] [TOKEN_ALIGN] Truncated recv_tensor to %ld tokens\n",
            aligned_tokens);
      }

      // 对 slot_mapping 进行切片对齐
      if (slot_mapping_tokens > aligned_tokens) {
        task.slot_mapping = task.slot_mapping.narrow(0, 0, aligned_tokens);
        DEBUG_PRINTF(
            "[KvComm] [TOKEN_ALIGN] Truncated slot_mapping to %ld tokens\n",
            aligned_tokens);
      }

      DEBUG_PRINTF(
          "[KvComm] [TOKEN_ALIGN] Alignment completed, proceeding with %ld "
          "tokens\n",
          aligned_tokens);
    }

    // 检查任务参数
    if (task.recv_tensor.device().type() != torch::kCUDA ||
        task.recv_tensor.device().index() != this->device_) {
      throw std::invalid_argument(
          "Tensor must be on the same CUDA device as the KvComm");
    }

    // 确保放置流已创建
    if (!this->placer_stream_) {
      throw std::runtime_error("Placer stream is not initialized");
    }

    // 检查 recv_tensor 第 0 维是否为 2
    if (task.recv_tensor.dim() < 2 || task.recv_tensor.size(0) != 2) {
      throw std::invalid_argument(
          std::string("recv_tensor must have at least 2 dimensions with size 2 "
                      "in the first dimension"));
    }

    // 使用调试工具记录张量信息
    LOG_TENSOR_BASIC_INFO(task.recv_tensor, "recv_tensor");
    LOG_TENSOR_SHAPE(task.recv_tensor, "recv_tensor");

    // 验证张量指针有效性
    void* recv_data_ptr = task.recv_tensor.data_ptr();
    if (!debug_utils::is_cuda_pointer_valid(
            recv_data_ptr,
            task.recv_tensor.numel() * task.recv_tensor.element_size())) {
      throw std::runtime_error("recv_tensor has invalid CUDA pointer");
    }

    auto key = task.recv_tensor.select(0, 0);
    auto value = task.recv_tensor.select(0, 1);

    // 记录 key 和 value 张量信息
    LOG_TENSOR_BASIC_INFO(key, "key");
    LOG_TENSOR_SHAPE(key, "key");
    LOG_TENSOR_BASIC_INFO(value, "value");
    LOG_TENSOR_SHAPE(value, "value");
    LOG_TENSOR_BASIC_INFO(task.slot_mapping, "slot_mapping");
    LOG_TENSOR_SHAPE(task.slot_mapping, "slot_mapping");

    // 验证 slot_mapping 的值
    auto slot_mapping_cpu = task.slot_mapping.cpu();
    auto slot_data = static_cast<int64_t*>(slot_mapping_cpu.data_ptr());
    VALIDATE_SLOT_MAPPING_SAMPLE(slot_data, task.slot_mapping.size(0),
                                 task.placer_info->block_size,
                                 "place_function");

    // 记录 placer_info 详细信息
    DEBUG_PRINTF(
        "[KvComm] [PLACER_INFO] block_size: %d, block_stride: %d, num_layers: "
        "%d\n",
        task.placer_info->block_size, task.placer_info->block_stride,
        task.placer_info->num_layers);
    DEBUG_PRINTF(
        "[KvComm] [PLACER_INFO] num_heads: %d, head_size: %d, kv_cache_dtype: "
        "%s\n",
        task.placer_info->num_heads, task.placer_info->head_size,
        task.placer_info->kv_cache_dtype.c_str());
    DEBUG_PRINTF("[KvComm] [PLACER_INFO] k_scale: %f, v_scale: %f\n",
                 task.placer_info->k_scale, task.placer_info->v_scale);

    // 记录缓存层指针信息
    LOG_TENSOR_BASIC_INFO(task.placer_info->key_cache_layer_ptrs,
                          "key_cache_layer_ptrs");
    LOG_TENSOR_SHAPE(task.placer_info->key_cache_layer_ptrs,
                     "key_cache_layer_ptrs");
    LOG_TENSOR_BASIC_INFO(task.placer_info->value_cache_layer_ptrs,
                          "value_cache_layer_ptrs");
    LOG_TENSOR_SHAPE(task.placer_info->value_cache_layer_ptrs,
                     "value_cache_layer_ptrs");

    // 验证缓存层指针的有效性
    auto key_ptrs_cpu = task.placer_info->key_cache_layer_ptrs.cpu();
    auto value_ptrs_cpu = task.placer_info->value_cache_layer_ptrs.cpu();
    auto key_ptrs_data = static_cast<void**>(key_ptrs_cpu.data_ptr());
    auto value_ptrs_data = static_cast<void**>(value_ptrs_cpu.data_ptr());

    LOG_CACHE_POINTERS(key_ptrs_data, task.placer_info->num_layers, "key");
    LOG_CACHE_POINTERS(value_ptrs_data, task.placer_info->num_layers, "value");

    // 全面的输入验证
    VALIDATE_BASIC_POINTERS(key.data_ptr(), value.data_ptr(),
                            task.slot_mapping.data_ptr(), "KvComm::place");
    VALIDATE_CACHE_POINTERS(key_ptrs_data, value_ptrs_data, "KvComm::place");
    VALIDATE_DIMENSIONS(task.placer_info->num_layers, key.size(0),
                        task.placer_info->num_heads,
                        task.placer_info->head_size, "KvComm::place");
    VALIDATE_BLOCK_PARAMS(task.placer_info->block_size,
                          task.placer_info->block_stride, "KvComm::place");

    DEBUG_PRINTF("[KvComm] Placing tensor with request ID %s\n",
                 task.request_id.c_str());

    // CUDA 流状态检查和设备内存信息
    debug_utils::log_stream_status(this->placer_stream_, "before_kernel");
    debug_utils::log_device_memory_info(this->device_);

    // 检查 CUDA 设备状态
    cudaError_t device_err = cudaGetLastError();
    if (device_err != cudaSuccess) {
      DEBUG_PRINTF("[KvComm] [CUDA_ERROR] Pre-kernel CUDA error detected: %s\n",
                   cudaGetErrorString(device_err));
      throw std::runtime_error("Pre-kernel CUDA error: " +
                               std::string(cudaGetErrorString(device_err)));
    }

    // 调用 CUDA 内核进行放置
    DEBUG_PRINTF(
        "[KvComm] [KERNEL_CALL] Calling "
        "fused_reshape_and_cache_flash_layers\n");
    fused_reshape_and_cache_flash_layers(
        key,                                       // 接收的 key
        value,                                     // 接收的 value
        task.placer_info->key_cache_layer_ptrs,    // key block table 层指针
        task.placer_info->value_cache_layer_ptrs,  // value block table 层指针
        task.placer_info->block_size, task.placer_info->block_stride,
        task.slot_mapping,  // token slot 映射
        task.placer_info->kv_cache_dtype, task.placer_info->k_scale,
        task.placer_info->v_scale, this->placer_stream_);

    // 检查内核启动后的错误
    cudaError_t kernel_launch_err = cudaGetLastError();
    if (kernel_launch_err != cudaSuccess) {
      DEBUG_PRINTF("[KvComm] [CUDA_ERROR] Kernel launch error: %s\n",
                   cudaGetErrorString(kernel_launch_err));
      throw std::runtime_error(
          "CUDA kernel launch failed: " +
          std::string(cudaGetErrorString(kernel_launch_err)));
    }

    DEBUG_PRINTF("[KvComm] Waiting Placing End with request ID %s\n",
                 task.request_id.c_str());

    // 确保流同步
    DEBUG_PRINTF("[KvComm] [STREAM_SYNC] Starting stream synchronization\n");
    debug_utils::log_stream_status(this->placer_stream_, "before_sync");

    cudaError_t err = cudaStreamSynchronize(this->placer_stream_);
    if (err != cudaSuccess) {
      DEBUG_PRINTF("[KvComm] [CUDA_ERROR] Stream synchronization failed: %s\n",
                   cudaGetErrorString(err));

      // 记录后同步状态和内存信息
      debug_utils::log_stream_status(this->placer_stream_, "after_failed_sync");
      debug_utils::log_device_memory_info(this->device_);

      // 详细的错误诊断信息
      if (!task.recv_tensor.defined()) {
        DEBUG_PRINTF("[KvComm] [ERROR_DIAG] recv_tensor is not defined\n");
        throw std::invalid_argument("Tensor is not defined");
      }
      if (task.recv_tensor.device().type() != torch::kCUDA ||
          task.recv_tensor.device().index() != this->device_) {
        DEBUG_PRINTF(
            "[KvComm] [ERROR_DIAG] recv_tensor device mismatch: "
            "tensor_device=%d, expected_device=%d\n",
            task.recv_tensor.device().index(), this->device_);
        throw std::invalid_argument(
            "Tensor must be on the same CUDA device as the Pipe");
      }
      if (!task.recv_tensor.is_contiguous()) {
        DEBUG_PRINTF("[KvComm] [ERROR_DIAG] recv_tensor is not contiguous\n");
        throw std::invalid_argument("Tensor must be contiguous");
      }
      void* buf = task.recv_tensor.data_ptr();
      if (buf == nullptr) {
        DEBUG_PRINTF("[KvComm] [ERROR_DIAG] recv_tensor data_ptr is nullptr\n");
        throw std::runtime_error("Tensor data_ptr is nullptr");
      }

      // 检查是否是内存访问错误
      if (err == cudaErrorIllegalAddress) {
        DEBUG_PRINTF("[KvComm] [ERROR_DIAG] ILLEGAL MEMORY ACCESS detected!\n");
        DEBUG_PRINTF(
            "[KvComm] [ERROR_DIAG] This suggests out-of-bounds memory access "
            "in the kernel\n");
        DEBUG_PRINTF(
            "[KvComm] [ERROR_DIAG] Check slot_mapping values and cache pointer "
            "validity\n");
      }

      DEBUG_PRINTF(
          "[KvComm] [ERROR_DIAG] All tensor checks passed, stream sync error "
          "is: %s\n",
          cudaGetErrorString(err));
      throw std::runtime_error("Failed to synchronize placer stream: " +
                               std::string(cudaGetErrorString(err)));
    }

    // 成功同步后的状态检查
    debug_utils::log_stream_status(this->placer_stream_,
                                   "after_successful_sync");

    DEBUG_PRINTF(
        "[KvComm] [PLACE_SUCCESS] Placing completed for request ID %s\n",
        task.request_id.c_str());
  }

 private:
  auto get_data_pipe(int src, int dst) -> std::shared_ptr<Pipe> {
    if (src < 0 || src >= this->size_ || dst < 0 || dst >= this->size_ ||
        src == dst) {
      throw std::out_of_range(
          "Source or destination rank wrong: " + std::to_string(src) + " -> " +
          std::to_string(dst));
    }
    return (src < dst) ? this->data_pipe_up_ : this->data_pipe_down_;
  }

  auto get_stream(int src, int dst) -> cudaStream_t {
    if (src < 0 || src >= this->size_ || dst < 0 || dst >= this->size_ ||
        src == dst) {
      throw std::out_of_range(
          "Source or destination rank wrong: " + std::to_string(src) + " -> " +
          std::to_string(dst));
    }
    return this->stream_matrix_[src][dst];
  }

 private:
  // 基本属性
  int rank_ = 0;
  int size_ = 0;
  int device_ = -1;  // -1 表示 CPU, 其他值表示 GPU 设备编号

  std::shared_ptr<Pipe> signal_pipe_;
  std::shared_ptr<Pipe> ack_pipe_;
  std::shared_ptr<Pipe> data_pipe_up_;
  std::shared_ptr<Pipe> data_pipe_down_;
  std::vector<std::vector<cudaStream_t>> stream_matrix_;
  cudaStream_t placer_stream_ = nullptr;

  int poll_counter_ = 0;  // 用于轮询计数
};

// 缓存管理和通信任务调度
class KvBuffer {
 public:
  KvBuffer(std::string host, int port, int rank, int size, int tp_rank,
           int tp_size, int device, size_t send_buffer_size_threshold,
           size_t recv_buffer_size_threshold) {
    this->rank_ = rank;
    this->size_ = size;
    this->tp_rank_ = tp_rank;
    this->tp_size_ = tp_size;
    this->device_ = device;
    this->send_buffer_size_threshold_ = send_buffer_size_threshold;
    this->recv_buffer_size_threshold_ = recv_buffer_size_threshold;

    // 检查参数合法性
    if (rank < 0 || rank >= size) {
      throw std::invalid_argument(
          "Rank must be in [0, size), but got rank=" + std::to_string(rank) +
          ", size=" + std::to_string(size));
    }
    if (tp_rank < 0 || tp_rank >= tp_size) {
      throw std::invalid_argument(
          "Tensor Parallel Rank must be in [0, tp_size), but got tp_rank=" +
          std::to_string(tp_rank) + ", tp_size=" + std::to_string(tp_size));
    }

    cudaSetDevice(device);

    // 初始化 KV 通信器
    this->kv_comm_ = std::make_shared<KvComm>(
        host, port, rank, size, tp_rank, device, recv_buffer_size_threshold);

    // 初始化 TP 同步器
    std::vector<std::string> channels = {"send", "recv", "place"};
    int syncer_port =
        port + KvComm::NUM_PIPE_PER_RANK * tp_size + rank * channels.size();
    this->tp_syncer_ = std::make_shared<TCPSynchronizer>(
        tp_rank, tp_size, channels, host, syncer_port);

    printf(
        "[KvBuffer] Initialized with rank %d, size %d, tp_rank %d, "
        "tp_size %d, device %d\n",
        rank, size, tp_rank, tp_size, device);

    this->init_background_threads();
  }

  // 初始化放置参数
  auto init_placer_info(torch::Tensor key_cache_layer_ptrs,
                        torch::Tensor value_cache_layer_ptrs, int block_size,
                        int block_stride, std::string kv_cache_dtype,
                        double k_scale, double v_scale, int num_layers,
                        int num_heads, int head_size) -> void {
    this->placer_info_ = std::make_shared<PlacerInfo>(PlacerInfo{
        key_cache_layer_ptrs, value_cache_layer_ptrs, block_size, block_stride,
        kv_cache_dtype, k_scale, v_scale, num_layers, num_heads, head_size});
  }

  ~KvBuffer() {
    // Properly shutdown the buffer to prevent memory leaks
    this->close();
  }

  // 检查发送缓冲区空间是否足够
  auto can_send(int64_t new_size) -> bool {
    return this->send_buffer_size_ + new_size <
           this->send_buffer_size_threshold_;
  }

  // 添加发送任务
  auto add_send_task(std::string request_id, int recv_rank,
                     torch::Tensor kv) -> void {
    size_t data_size_bytes = kv.numel() * kv.element_size();

    // DEBUG: Print tensor reference count when adding task
    printf(
        "[TENSOR_DEBUG] ADD_SEND_TASK - Request: %s, Tensor ptr: %p, "
        "use_count: %ld, data_ptr: %p, size: %.1fMB\n",
        request_id.c_str(), &kv, kv.use_count(), kv.data_ptr(),
        data_size_bytes / (1024.0 * 1024.0));

    std::lock_guard<std::mutex> lock(this->send_mutex_);

    // 检查存在性
    if (this->send_tasks_.find(request_id) != this->send_tasks_.end()) {
      throw std::runtime_error("Send task with request_id already exists: " +
                               request_id);
    }

    // 记录任务入队时间
    auto enqueue_time = std::chrono::high_resolution_clock::now();

    // 加入发送任务
    this->send_tasks_.emplace(
        request_id, SendTask{request_id, recv_rank, kv, enqueue_time});

    // DEBUG: Print tensor reference count after storing in map
    auto& stored_task = this->send_tasks_[request_id];
    printf(
        "[TENSOR_DEBUG] AFTER_MAP_INSERT - Request: %s, Map tensor ptr: %p, "
        "use_count: %ld, data_ptr: %p\n",
        request_id.c_str(), &stored_task.kv, stored_task.kv.use_count(),
        stored_task.kv.data_ptr());

    // 更新发送缓冲区大小
    this->send_buffer_size_ += data_size_bytes;
  }

  auto add_place_task(std::string request_id,
                      std::vector<int> slot_mapping) -> void {
    // 将 slot_mapping 转换为 Tensor
    torch::Tensor slot_mapping_tensor =
        torch::tensor(slot_mapping, torch::kInt64)
            .to(torch::kCUDA, this->device_);

    std::lock_guard<std::mutex> lock(this->place_mutex_);

    // 检查存在性
    if (this->place_tasks_.find(request_id) != this->place_tasks_.end()) {
      throw std::runtime_error("Place task with request_id already exists: " +
                               request_id);
    }

    // 检查放置信息是否已初始化
    if (!this->placer_info_) {
      throw std::runtime_error("Placer info is not initialized");
    }

    // 添加 place 任务
    this->place_tasks_.emplace(
        request_id,
        PlaceTask{request_id, slot_mapping_tensor, {}, this->placer_info_});
  }

  // 查询放置完成
  auto query_place_done(std::string request_id) -> bool {
    std::lock_guard<std::mutex> lock(this->place_mutex_);
    return this->place_done_marks_.find(request_id) !=
           this->place_done_marks_.end();
  }

  // 关闭 KvBuffer 并清理资源
  auto close() -> void {
    printf("[KvBuffer] Shutting down KvBuffer...\n");

    // 设置停止标志
    this->stop_flag_ = true;

    // 等待所有后台线程结束
    for (auto& thread : this->background_threads_) {
      if (thread.joinable()) {
        thread.join();
      }
    }

    // 清理发送任务和缓冲区
    {
      std::lock_guard<std::mutex> lock(this->send_mutex_);
      printf("[KvBuffer] Clearing %zu send tasks\n", this->send_tasks_.size());
      this->send_tasks_.clear();
      this->send_buffer_size_ = 0;
    }

    // 清理接收缓冲区
    {
      std::lock_guard<std::mutex> lock(this->recv_mutex_);
      printf("[KvBuffer] Clearing %zu recv buffer entries\n",
             this->recv_buffer_.size());
      this->recv_buffer_.clear();
      this->recv_buffer_size_ = 0;
    }

    // 清理放置任务
    {
      std::lock_guard<std::mutex> lock(this->place_mutex_);
      printf("[KvBuffer] Clearing %zu place tasks\n",
             this->place_tasks_.size());
      this->place_tasks_.clear();
      this->place_done_marks_.clear();
    }

    printf("[KvBuffer] KvBuffer shutdown complete\n");
  }

 private:
  // 判断当前进程是否是主 TP Worker
  auto is_main_tp_worker() -> bool { return (this->tp_rank_ == 0); }

  auto init_background_threads() -> void {
    for (const auto& server_type : {"send", "recv", "place"}) {
      this->background_threads_.emplace_back(&KvBuffer::background_server_loop,
                                             this, server_type);
    }
  }

  // 后台服务
  auto background_server_loop(std::string server_type) -> void {
    // 设定后台线程的 cuda 上下文
    cudaSetDevice(this->device_);

    while (!this->stop_flag_) {
      if (server_type == "send") {
        std::optional<SendTask> send_task_opt = this->schedule_send_task();
        if (send_task_opt.has_value()) {
          std::string request_id =
              send_task_opt.value().request_id;  // Save request_id before move

          // Generate a random ID for this execution to match before/after logs
          static int execution_counter =
              rand() % 100000000;  // Start with random number
          int execution_id = ++execution_counter;

          // Check CUDA memory before task execution
          this->debug_check_cuda_memory_before_task(request_id, execution_id);

          this->execute_send_task(std::move(send_task_opt.value()));  // ✅ MOVE to avoid copy

          // Check CUDA memory after task execution
          this->debug_check_cuda_memory_after_task(request_id, execution_id);
        } else {
          // 如果没有发送任务, 等待一段时间
          std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
      } else if (server_type == "recv") {
        std::optional<RecvTask> recv_task_opt = this->schedule_recv_task();
        if (recv_task_opt.has_value()) {
          this->execute_recv_task(recv_task_opt.value());
        } else {
          // 如果没有接收任务, 等待一段时间
          std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
      } else if (server_type == "place") {
        std::optional<PlaceTask> place_task_opt = this->schedule_place_task();
        if (place_task_opt.has_value()) {
          this->execute_place_task(place_task_opt.value());
        } else {
          // 如果没有放置任务, 等待一段时间
          std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
      } else {
        throw std::invalid_argument("Unknown server type: " + server_type);
      }
    }
  }

  // 后台线程调度发送任务
  auto schedule_send_task() -> std::optional<SendTask> {
    std::lock_guard<std::mutex> lock(this->send_mutex_);
    // 如果没有任务, 返回空
    if (this->send_tasks_.empty()) {
      return {};
    }
    // 移除第一个元素并返回 (使用 move 避免不必要的拷贝)
    auto it = this->send_tasks_.begin();
    SendTask task = std::move(it->second);  // ✅ MOVE instead of copy

    // DEBUG: Print tensor reference count when scheduling task
    printf(
        "[TENSOR_DEBUG] SCHEDULE_TASK - Request: %s, Tensor ptr: %p, "
        "use_count: %ld, data_ptr: %p\n",
        task.request_id.c_str(), &task.kv, task.kv.use_count(),
        task.kv.data_ptr());

    this->send_tasks_.erase(it);  // 从任务列表中移除

    // DEBUG: Print tensor reference count after removing from map
    printf(
        "[TENSOR_DEBUG] AFTER_MAP_ERASE - Request: %s, Tensor ptr: %p, "
        "use_count: %ld, data_ptr: %p\n",
        task.request_id.c_str(), &task.kv, task.kv.use_count(),
        task.kv.data_ptr());

    return task;
  }

  // 后台线程调度接收任务
  auto schedule_recv_task() -> std::optional<RecvTask> {
    // 等待接收缓冲区空间
    size_t log_cnt = 0;
    while (this->recv_buffer_size_ > this->recv_buffer_size_threshold_) {
      // 如果接收缓冲区已满, 等待一段时间
      if (log_cnt++ % 1'000'000'000 == 0) {
        printf(
            "[KvBuffer] Warning: Waiting for recv buffer space, current size: "
            "%ld\n",
            this->recv_buffer_size_.load());
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    std::optional<RecvTask> recv_task_opt =
        this->kv_comm_->poll();  // 轮询接收任务
    if (recv_task_opt.has_value()) {
      printf("[KvBuffer] Scheduled recv task %s from rank %d\n",
             recv_task_opt.value().request_id.c_str(),
             recv_task_opt.value().send_rank);
    }
    return recv_task_opt;  // 返回接收任务
  }

  // 后台线程调度放置任务
  auto schedule_place_task() -> std::optional<PlaceTask> {
    std::lock_guard<std::mutex> lock(this->place_mutex_);
    if (this->place_tasks_.empty()) {
      return {};
    }

    // PlaceTask
    std::lock_guard<std::mutex> recv_lock(this->recv_mutex_);
    if (this->recv_buffer_.empty()) {
      return {};
    }

    // 查找 recv_buffer_ 和 place_tasks_ 中 request_id 的交集, 并返回一个
    for (auto it = this->place_tasks_.begin(); it != this->place_tasks_.end();
         ++it) {
      const std::string& request_id = it->first;
      if (this->recv_buffer_.find(request_id) != this->recv_buffer_.end()) {
        // 找到共同的 request_id, 配置 PlaceTask 并从对应列表中移除任务数据.
        PlaceTask task = it->second;
        task.recv_tensor = this->recv_buffer_[request_id];
        this->recv_buffer_.erase(request_id);  // 从接收缓冲区中移除
        this->place_tasks_.erase(it);          // 从任务列表中移除
        return task;
      }
    }

    // 如果没有找到共同的 request_id, 返回空的 PlaceTask
    return {};
  }

  // 后台线程执行发送任务
  auto execute_send_task(SendTask&& send_task) -> void {
    // DEBUG: Print tensor reference count at function entry (after move)
    printf(
        "[TENSOR_DEBUG] EXECUTE_TASK_ENTRY - Request: %s, Tensor ptr: %p, use_count: "
        "%ld, data_ptr: %p\n",
        send_task.request_id.c_str(), &send_task.kv, send_task.kv.use_count(),
        send_task.kv.data_ptr());

    // Calculate data size in bytes and MB
    size_t data_size_bytes = send_task.kv.numel() * send_task.kv.element_size();
    double data_size_gb =
        static_cast<double>(data_size_bytes) / (1024.0 * 1024.0 * 1024.0);

    // DEBUG: Print tensor reference count before send
    printf(
        "[TENSOR_DEBUG] BEFORE SEND - Request: %s, Tensor ptr: %p, use_count: "
        "%ld, data_ptr: %p, size: %.1fMB\n",
        send_task.request_id.c_str(), &send_task.kv, send_task.kv.use_count(),
        send_task.kv.data_ptr(), data_size_bytes / (1024.0 * 1024.0));

    // Measure send operation execution time
    auto send_start_time = std::chrono::high_resolution_clock::now();

    try {
      // DEBUG: Check tensor state before sending
      if (!send_task.kv.defined()) {
        printf("[TENSOR_DEBUG] ERROR: Tensor not defined before send\n");
        throw std::invalid_argument("Tensor not defined");
      }

      // Send the tensor
      this->kv_comm_->send(send_task);
      this->send_buffer_size_ -= data_size_bytes;

      // DEBUG: Print tensor reference count after send
      printf(
          "[TENSOR_DEBUG] AFTER SEND - Request: %s, Tensor ptr: %p, use_count: "
          "%ld, data_ptr: %p\n",
          send_task.request_id.c_str(), &send_task.kv, send_task.kv.use_count(),
          send_task.kv.data_ptr());

      auto send_end_time = std::chrono::high_resolution_clock::now();

      // Calculate send operation duration
      auto send_duration = send_end_time - send_start_time;
      double send_execution_seconds =
          std::chrono::duration<double>(send_duration).count();

      auto send_task_duration = send_end_time - send_task.enqueue_time;
      double send_task_seconds =
          std::chrono::duration<double>(send_task_duration).count();

      // Enhanced performance metrics output
      printf("[KvBuffer] Sent task %s to rank %d\n",
             send_task.request_id.c_str(), send_task.recv_rank);
      printf(
          "[KvBuffer] Request ID: %s, Data Size: %.3f GB, Send Time: %.6f s, "
          "Total Latency: %.6f s\n",
          send_task.request_id.c_str(), data_size_gb, send_execution_seconds,
          send_task_seconds);

    } catch (const std::exception& e) {
      // 发送失败时恢复缓冲区大小
      this->send_buffer_size_ -= data_size_bytes;
      printf(
          "[TENSOR_DEBUG] EXCEPTION - Request: %s, Tensor ptr: %p, use_count: "
          "%ld, error: %s\n",
          send_task.request_id.c_str(), &send_task.kv, send_task.kv.use_count(),
          e.what());
      printf("[KvBuffer] Failed to send task %s: %s\n",
             send_task.request_id.c_str(), e.what());
      throw;
    }

    // DEBUG: Print tensor reference count before method exit (should be the
    // last reference)
    printf(
        "[TENSOR_DEBUG] BEFORE METHOD EXIT - Request: %s, Tensor ptr: %p, "
        "use_count: %ld, data_ptr: %p\n",
        send_task.request_id.c_str(), &send_task.kv, send_task.kv.use_count(),
        send_task.kv.data_ptr());

    // DEBUG: Check CUDA memory before tensor destruction
    size_t free_before, total;
    cudaMemGetInfo(&free_before, &total);
    printf(
        "[CUDA_DEBUG] BEFORE TENSOR DESTRUCTION - Request: %s, Free CUDA "
        "memory: %.1fMB\n",
        send_task.request_id.c_str(), free_before / (1024.0 * 1024.0));

    // 任务完成后，tensor 会自动释放（send_task 超出作用域）
    // 这确保了 torch::Tensor 的引用计数正确递减
    // DEBUG: The tensor should be released when this method exits and send_task
    // goes out of scope
  }
  // NOTE: send_task destructor is called here, tensor should be freed

  // Helper method to check CUDA memory before tensor execution
  void debug_check_cuda_memory_before_task(const std::string& request_id,
                                           int execution_id) {
    size_t free_before, total;
    cudaMemGetInfo(&free_before, &total);
    printf(
        "[CUDA_DEBUG] BEFORE TASK EXECUTION - Request: %s, ExecutionID: %d, "
        "Free CUDA "
        "memory: %.1fMB\n",
        request_id.c_str(), execution_id, free_before / (1024.0 * 1024.0));
  }

  // Helper method to check CUDA memory after tensor destruction
  void debug_check_cuda_memory_after_task(const std::string& request_id,
                                          int execution_id) {
    size_t free_after, total;
    cudaMemGetInfo(&free_after, &total);
    printf(
        "[CUDA_DEBUG] AFTER TASK EXECUTION - Request: %s, ExecutionID: %d, "
        "Free CUDA "
        "memory: %.1fMB\n",
        request_id.c_str(), execution_id, free_after / (1024.0 * 1024.0));
  }

  // 后台线程执行接收任务
  auto execute_recv_task(RecvTask recv_task) -> void {
    torch::Tensor tensor = this->kv_comm_->recv(recv_task);
    std::lock_guard<std::mutex> lock(this->recv_mutex_);
    this->recv_buffer_[recv_task.request_id] = tensor;

    this->recv_buffer_size_ += tensor.numel() * tensor.element_size();
    if (this->recv_buffer_size_ > this->recv_buffer_size_threshold_) {
      printf(
          "[KvBuffer] Warning: recv buffer size exceeded threshold, "
          "current size: %ld\n",
          this->recv_buffer_size_.load());
    }

    printf("[KvBuffer] Received task %s from rank %d\n",
           recv_task.request_id.c_str(), recv_task.send_rank);
  }

  // 后台线程执行放置任务
  auto execute_place_task(PlaceTask place_task) -> void {
    // reshape to [2, layer, *, num_heads, head_size]
    place_task.recv_tensor = place_task.recv_tensor.reshape(
        {2, this->placer_info_->num_layers, -1, this->placer_info_->num_heads,
         this->placer_info_->head_size});
    this->kv_comm_->place(place_task);
    std::lock_guard<std::mutex> lock(this->place_mutex_);
    this->place_done_marks_.insert(place_task.request_id);
    printf("[KvBuffer] Placed task %s\n", place_task.request_id.c_str());

    this->recv_buffer_size_ -=
        place_task.recv_tensor.numel() * place_task.recv_tensor.element_size();
    if (this->recv_buffer_size_ < 0) {
      printf(
          "[KvBuffer] Warning: recv buffer size went negative, "
          "current size: %ld\n",
          this->recv_buffer_size_.load());
      this->recv_buffer_size_ = 0;  // 重置为 0
    }
  }

 private:
  // 基本属性
  int rank_ = 0;
  int size_ = 0;
  int tp_rank_ = 0;  // Tensor Parallel Rank
  int tp_size_ = 0;  // Tensor Parallel Size
  int device_ = -1;
  int64_t send_buffer_size_threshold_ = 0;  // 发送缓冲区大小阈值
  std::atomic<int64_t> send_buffer_size_ = 0;
  int64_t recv_buffer_size_threshold_ = 0;  // 接收缓冲区大小阈值
  std::atomic<int64_t> recv_buffer_size_ = 0;
  std::shared_ptr<PlacerInfo> placer_info_;  // 放置信息

  // 通信对象
  std::shared_ptr<KvComm> kv_comm_;             // KV 通信对象
  std::shared_ptr<TCPSynchronizer> tp_syncer_;  // Tensor Parallel 同步器

  // 任务队列
  std::map<std::string, SendTask> send_tasks_;  // 发送任务缓存
  std::unordered_map<std::string, PlaceTask> place_tasks_;  // 放置任务缓存

  // 缓冲区
  std::unordered_map<std::string, torch::Tensor> recv_buffer_;  // 接收缓冲区
  std::unordered_set<std::string> place_done_marks_;  // 完成 place 的标志

  // 并发控制
  std::mutex send_mutex_;   // 发送任务互斥锁
  std::mutex recv_mutex_;   // 接收任务互斥锁
  std::mutex place_mutex_;  // 放置任务互斥锁

  // 线程管理
  std::vector<std::thread> background_threads_;  // 工作线程列表
  std::atomic<bool> stop_flag_{false};           // 停止标志
};

// 模块封装代码
PYBIND11_MODULE(kv_buffer, m) {
  pybind11::class_<SendTask, std::shared_ptr<SendTask>>(m, "SendTask")
      .def(pybind11::init(
               [](std::string request_id, int recv_rank, torch::Tensor kv) {
                 auto enqueue_time = std::chrono::high_resolution_clock::now();
                 return SendTask{request_id, recv_rank, kv, enqueue_time};
               }),
           pybind11::arg("request_id"), pybind11::arg("recv_rank"),
           pybind11::arg("kv"))
      .def_readwrite("request_id", &SendTask::request_id)
      .def_readwrite("recv_rank", &SendTask::recv_rank)
      .def_readwrite("kv", &SendTask::kv);

  pybind11::class_<RecvTask, std::shared_ptr<RecvTask>>(m, "RecvTask")
      .def(pybind11::init<std::string, int>(), pybind11::arg("request_id"),
           pybind11::arg("send_rank"))
      .def_readwrite("request_id", &RecvTask::request_id)
      .def_readwrite("send_rank", &RecvTask::send_rank);

  pybind11::class_<PlacerInfo, std::shared_ptr<PlacerInfo>>(m, "PlacerInfo")
      .def(pybind11::init<torch::Tensor, torch::Tensor, int, int, std::string,
                          double, double, int, int, int>(),
           pybind11::arg("key_cache_layer_ptrs"),
           pybind11::arg("value_cache_layer_ptrs"), pybind11::arg("block_size"),
           pybind11::arg("block_stride"), pybind11::arg("kv_cache_dtype"),
           pybind11::arg("k_scale"), pybind11::arg("v_scale"),
           pybind11::arg("num_layers"), pybind11::arg("num_heads"),
           pybind11::arg("head_size"))
      .def_readwrite("key_cache_layer_ptrs", &PlacerInfo::key_cache_layer_ptrs)
      .def_readwrite("value_cache_layer_ptrs",
                     &PlacerInfo::value_cache_layer_ptrs)
      .def_readwrite("block_size", &PlacerInfo::block_size)
      .def_readwrite("block_stride", &PlacerInfo::block_stride)
      .def_readwrite("kv_cache_dtype", &PlacerInfo::kv_cache_dtype)
      .def_readwrite("k_scale", &PlacerInfo::k_scale)
      .def_readwrite("v_scale", &PlacerInfo::v_scale)
      .def_readwrite("num_layers", &PlacerInfo::num_layers)
      .def_readwrite("num_heads", &PlacerInfo::num_heads)
      .def_readwrite("head_size", &PlacerInfo::head_size);

  pybind11::class_<PlaceTask, std::shared_ptr<PlaceTask>>(m, "PlaceTask")
      .def(pybind11::init<std::string, torch::Tensor, torch::Tensor,
                          std::shared_ptr<PlacerInfo>>(),
           pybind11::arg("request_id"), pybind11::arg("slot_mapping"),
           pybind11::arg("recv_tensor"), pybind11::arg("placer_info"))
      .def_readwrite("request_id", &PlaceTask::request_id)
      .def_readwrite("slot_mapping", &PlaceTask::slot_mapping)
      .def_readwrite("recv_tensor", &PlaceTask::recv_tensor)
      .def_readwrite("placer_info", &PlaceTask::placer_info);

  pybind11::class_<KvComm, std::shared_ptr<KvComm>>(m, "KvComm")
      .def(pybind11::init<std::string, int, int, int, int, int, size_t>(),
           pybind11::arg("host"), pybind11::arg("port"), pybind11::arg("rank"),
           pybind11::arg("size"), pybind11::arg("tp_rank"),
           pybind11::arg("device"), pybind11::arg("recv_buffer_size_threshold"))
      .def("send", &KvComm::send)
      .def("poll", &KvComm::poll)
      .def("recv", &KvComm::recv)
      .def("place", &KvComm::place);

  pybind11::class_<KvBuffer, std::shared_ptr<KvBuffer>>(m, "KvBuffer")
      .def(pybind11::init<std::string, int, int, int, int, int, int, size_t,
                          size_t>(),
           pybind11::arg("host"), pybind11::arg("port"), pybind11::arg("rank"),
           pybind11::arg("size"), pybind11::arg("tp_rank"),
           pybind11::arg("tp_size"), pybind11::arg("device"),
           pybind11::arg("send_buffer_size_threshold"),
           pybind11::arg("recv_buffer_size_threshold"))
      .def("init_placer_info", &KvBuffer::init_placer_info)
      .def("can_send", &KvBuffer::can_send)
      .def("add_send_task", &KvBuffer::add_send_task)
      .def("add_place_task", &KvBuffer::add_place_task)
      .def("query_place_done", &KvBuffer::query_place_done)
      .def("close", &KvBuffer::close);
}