"""
Simple KV Cache Connector for Distributed Machine Learning Inference

The SimpleConnector transfers KV caches between prefill vLLM worker (KV cache 
producer) and decode vLL<PERSON> worker (KV cache consumer) using PyNcclPipe.

But the logic can be extended to support other pipe and lookup buffer.
"""

from typing import TYPE_CHECKING, List, Optional, Tuple, Union, Dict

import torch
from functools import partial

from abc import ABC, abstractmethod

from vllm import _custom_ops as ops
from vllm.config import (
    CacheConfig,
    ParallelConfig,
    ModelConfig,
    VllmConfig,
    KVTransferConfig,
)
from vllm.distributed.kv_transfer.kv_lookup_buffer.simple_buffer import (
    SimpleBuffer,
    PlacerInfo,
)
from vllm.logger import init_logger
from vllm.sequence import IntermediateTensors
from vllm.utils import STR_DTYPE_TO_TORCH_DTYPE, get_dtype_size, CodeTimer

import kv_placer

if TYPE_CHECKING:
    from vllm.worker.model_runner import ModelInputForGPUWithSamplingMetadata

logger = init_logger(__name__)


class LayerInfo:
    """模型层信息, 用于保存 KV Cache"""

    def __init__(self, model_executable: torch.nn.Module):
        self.start_layer = model_executable.model.start_layer
        self.end_layer = model_executable.model.end_layer
        self.layer_attn = []
        for i in range(self.start_layer, self.end_layer):
            layer = model_executable.model.layers[i]
            attn_info = (
                layer.self_attn.attn.kv_cache_dtype,
                layer.self_attn.attn._k_scale,
                layer.self_attn.attn._v_scale,
            )
            self.layer_attn.append(attn_info)

    def __repr__(self):
        return f"LayerInfo(start_layer={self.start_layer}, end_layer={self.end_layer}, layers = {self.layer_attn})"


class ConnectorBase(ABC):

    def __init__(
        self,
        worker_rank: int,
        local_rank: int,
        vllm_config: VllmConfig,
    ):
        pass

    @abstractmethod
    def init_model_info(self, model: torch.nn.Module) -> None:
        pass

    @abstractmethod
    def init_kv_caches(self, kv_caches: List[List[torch.Tensor]]) -> None:
        pass

    @abstractmethod
    def can_buffer(self, num_blocks_required: int) -> bool:
        pass

    @abstractmethod
    def add_send_kv_task(
        self,
        model_input: "ModelInputForGPUWithSamplingMetadata",
        kv_caches: List[torch.Tensor],
    ) -> None:
        pass

    @abstractmethod
    def add_place_kv_task(
        self,
        request_id: str,
        slot_mapping: List[int],
    ) -> None:
        pass

    @abstractmethod
    def query_received(self, request_id: str) -> bool:
        pass

    @abstractmethod
    def close(self) -> None:
        pass


class SimpleConnector(ConnectorBase):
    """连接器, 用于连接 vllm 和 KV Buffer."""

    def __init__(
        self,
        worker_rank: int,
        local_rank: int,
        vllm_config: VllmConfig,
    ):
        """初始化流程:
        - 保存和分析配置
        - 构造 KV Cache Buffer
        - 构造通信控制变量
        """

        assert vllm_config.kv_transfer_config.is_kv_transfer_instance

        self.worker_rank: int = worker_rank  # TP worker ID (buffer ID)
        self.local_rank: int = local_rank  # GPU device ID
        self.kv_rank: int = vllm_config.kv_transfer_config.kv_rank  # vllm instance ID

        self.vllm_config: VllmConfig = vllm_config
        self.transfer_config: KVTransferConfig = vllm_config.kv_transfer_config
        self.parallel_config: ParallelConfig = vllm_config.parallel_config

        # The size of a block (vllm page) in bytes
        self.block_size_bytes = self._get_block_size_bytes(
            self.vllm_config.cache_config,
            self.vllm_config.model_config,
            self.vllm_config.parallel_config,
        )

        logger.info(
            "[Connector] Initializing PyNcclConfig under kv_transfer_config %s",
            self.transfer_config,
        )

        # KV Buffer: 用于暂存和收发 KV Cache
        self.kv_cache_buffer = SimpleBuffer(
            worker_rank,
            local_rank,
            self.transfer_config,
            self.parallel_config,
        )

        self.layer_info: Optional[LayerInfo] = None
        self.kv_caches: Optional[List[torch.Tensor]] = None

        logger.debug(f"[Connector] initialized for {self.kv_rank}")

    def init_model_info(self, model: torch.nn.Module) -> None:
        logger.debug("[Connector] Initializing model info for KV Connector")
        self.layer_info = LayerInfo(model)
        kv_cache_dtypes = [layer[0] for layer in self.layer_info.layer_attn]
        k_scales = [layer[1] for layer in self.layer_info.layer_attn]
        v_scales = [layer[2] for layer in self.layer_info.layer_attn]

        # 融合算子仅支持相同的 dtype 和 scale
        assert all(
            kv_cache_dtypes[0] == kv_cache_dtype for kv_cache_dtype in kv_cache_dtypes
        )
        assert all(k_scales[0] == k_scale for k_scale in k_scales)
        assert all(v_scales[0] == v_scale for v_scale in v_scales)

        logger.debug(f"[Connector] Layer info: {self.layer_info}")

    def init_kv_caches(self, kv_caches: List[List[torch.Tensor]]) -> None:
        logger.debug("[Connector] Initializing KV Cache for KV Connector")
        self.kv_caches: List[torch.Tensor] = kv_caches[0]  # 假设: 没有启用 pp
        # kv_caches: num_layers * [2, num_blocks, block_size, num_heads, head_size]
        self.key_cache_layer_ptrs = torch.tensor(
            [kv_cache_layer[0].data_ptr() for kv_cache_layer in self.kv_caches],
            dtype=torch.int64,
            device=self.kv_caches[0].device,
        )
        self.value_cache_layer_ptrs = torch.tensor(
            [kv_cache_layer[1].data_ptr() for kv_cache_layer in self.kv_caches],
            dtype=torch.int64,
            device=self.kv_caches[0].device,
        )

        self.block_size = self.kv_caches[0].size(2)
        self.block_stride = self.kv_caches[0].stride(1)

        assert self.layer_info is not None, "Layer info should be initialized first"

        logger.debug(
            f"[Connector] kv_cache shape of one layer: {self.kv_caches[0].shape}"
        )
        logger.debug(
            f"[Connector] block_size: {self.block_size}, block_stride: {self.block_stride}"
        )
        logger.debug(f"[Connector] key_cache_layer_ptrs: {self.key_cache_layer_ptrs}")

        placer_info = PlacerInfo(
            key_cache_layer_ptrs=self.key_cache_layer_ptrs,
            value_cache_layer_ptrs=self.value_cache_layer_ptrs,
            block_size=self.block_size,
            block_stride=self.block_stride,
            kv_cache_dtype=self.layer_info.layer_attn[0][0],
            k_scale=self.layer_info.layer_attn[0][1],
            v_scale=self.layer_info.layer_attn[0][2],
        )

        self.kv_cache_buffer.init_placer_info(
            placer_info, kv_placer.fused_reshape_and_cache_flash_layers
        )

        logger.debug("[Connector] KV Cache for buffer initialized")

    def can_buffer(self, num_blocks_required: int) -> bool:
        num_bytes_required = num_blocks_required * self.block_size_bytes
        return self.kv_cache_buffer.can_buffer(num_bytes_required)

    def add_send_kv_task(
        self,
        model_input: "ModelInputForGPUWithSamplingMetadata",
        kv_caches: List[torch.Tensor],
    ) -> None:
        """发送 KV Cache  到指定实例 (后台异步执行).
        实现步骤:
        对 Batch 中的每个请求:
        - 检查是否需要发送
        - 如需要, 准备待发送项 (K, V)
            - 从 kv_caches 中取出待发送项 (从 model_input 寻找)
        - 将待发送项加入 buffer (后台会异步发送)
        """

        # batch 信息
        request_ids = model_input.request_ids
        ctrl_send_targets = model_input.ctrl_send_targets
        seq_lens = model_input.attn_metadata.seq_lens
        prompt_lens = model_input.prompt_lens
        ctrl_should_send_kvs = model_input.ctrl_should_send_kvs

        logger.debug(f"[Connector Sender] request_ids: {request_ids}")
        logger.debug(f"[Connector Sender] ctrl_should_send_kvs: {ctrl_should_send_kvs}")

        for idx, request_id in enumerate(request_ids):
            if ctrl_should_send_kvs[idx] == False:
                continue  # 跳过没有发送标志的请求
            if seq_lens[idx] < prompt_lens[idx]:
                continue  # 跳过 prefill 未完成的请求 (chunked prefill)

            # 从 kv_caches 中取出待发送项
            kv = self._fetch_kv_from_block_table(
                model_input,
                idx,
                kv_caches,
            )

            ctrl_send_target = ctrl_send_targets[idx]
            logger.debug(
                f"[Connector] Preparing sending item for {request_id}: {self.kv_rank} -> {ctrl_send_target}."
            )

            # 添加到 buffer (异步发送)
            self.kv_cache_buffer.add_send_kv_task(request_id, ctrl_send_target, kv)

    def add_place_kv_task(self, request_id: str, slot_mapping: List[int]):
        """实现步骤:
        1. 构造 callback
        2. 传递给 buffer
        """
        assert self.layer_info is not None
        assert self.kv_caches is not None

        self.kv_cache_buffer.add_place_kv_task(request_id, slot_mapping)

    def query_received(self, request_id: str) -> bool:
        return self.kv_cache_buffer.query_received(request_id)

    # ---- Private Methods ----

    def _build_req_slot_mapping(
        self,
        model_input: "ModelInputForGPUWithSamplingMetadata",
        idx: int,
    ) -> torch.Tensor:
        """构建请求完整的 slot_mapping
        Block table 概念: 从 0 索引的一维数组 (每个 token 占据 1 slot)
        slot mapping 概念: 用于指向当前 token list 在 Block table 中的实际存储地址. (kv cache 指针)
        实现步骤:
        - 拼接 Block Table 中所有 Block 的 slot id
        - 限制长度为 seq_len
        - 返回值依照 curr_slot_mapping 格式
        """

        seq_len = model_input.attn_metadata.seq_lens[idx]
        block_table = model_input.attn_metadata.block_tables[idx]

        block_size = self.vllm_config.cache_config.block_size
        assert block_table.numel() > 0

        # 计算每个 block 的起始和结束位置
        start_indices = block_table * block_size
        # 创建一个范围张量，表示每个 block 的 slot 范围
        slot_ranges = torch.arange(block_size, device="cuda").unsqueeze(
            0
        ) + start_indices.unsqueeze(1)
        # 将 slot_ranges 展平并截取前 seq_len 个元素
        req_slot_mapping = slot_ranges.flatten()[:seq_len]

        return req_slot_mapping

    def _fetch_kv_from_block_table(
        self,
        model_input: "ModelInputForGPUWithSamplingMetadata",
        idx: int,
        kv_caches: List[torch.Tensor],
    ) -> torch.Tensor:
        # model 信息
        start_layer = self.layer_info.start_layer
        end_layer = self.layer_info.end_layer

        # 为了 chunked_prefill/decode 能传输全部的 KV Cache, 需要提供完整的 slot_mapping
        current_slot_mapping = self._build_req_slot_mapping(model_input, idx)

        # 按层合并 KV Cache
        keys, values = [], []
        for layer_id in range(start_layer, end_layer):
            kv_cache = kv_caches[layer_id - start_layer]

            _, _, num_heads, head_size = kv_cache[0].shape

            # (num_tokens, num_heads, head_size)
            key_cache = kv_cache[0].reshape(-1, num_heads, head_size)
            value_cache = kv_cache[1].reshape(-1, num_heads, head_size)

            # (num_layers, num_tokens, num_heads, head_size)
            keys.append(key_cache[current_slot_mapping].unsqueeze(0))
            values.append(value_cache[current_slot_mapping].unsqueeze(0))

        num_layer = end_layer - start_layer
        combined_kv = keys + values

        # (k/v, num_layers, num_tokens, num_heads, head_size)
        kv = (
            torch.cat(combined_kv, dim=0)
            .reshape(2, num_layer, -1, num_heads, head_size)
            .contiguous()
        )

        return kv

    def _get_block_size_bytes(
        self,
        cache_config: CacheConfig,
        model_config: ModelConfig,
        parallel_config: ParallelConfig,
    ) -> int:
        head_size = model_config.get_head_size()
        num_heads = model_config.get_num_kv_heads(parallel_config)
        num_attention_layers = model_config.get_num_attention_layers(parallel_config)

        key_cache_block = cache_config.block_size * num_heads * head_size
        value_cache_block = key_cache_block
        total = num_attention_layers * (key_cache_block + value_cache_block)

        if cache_config.cache_dtype == "auto":
            dtype = model_config.dtype
        else:
            dtype = STR_DTYPE_TO_TORCH_DTYPE[cache_config.cache_dtype]
        dtype_size = get_dtype_size(dtype)

        # 输出 token size in MiB (用于性能分析)
        token_size_bytes = head_size * num_heads * dtype_size * num_attention_layers * 2
        token_size_MiB = token_size_bytes / 1024 / 1024
        logger.debug(
            f"[Connector] TOKEN_SIZE for current parallel config: {token_size_MiB:.2f} MiB"
        )

        # 返回 block size in bytes
        return dtype_size * total

    def close(self):
        self.kv_cache_buffer.close()

class StubConnector(ConnectorBase):
    """使用 CPP 扩展的传输 (暂时为空实现)"""

    def __init__(
        self,
        worker_rank: int,
        local_rank: int,
        vllm_config: VllmConfig,
    ):
        pass

    def init_model_info(self, model: torch.nn.Module) -> None:
        pass

    def init_kv_caches(self, kv_caches: List[List[torch.Tensor]]) -> None:
        pass

    def can_buffer(self, num_blocks_required: int) -> bool:
        return True

    def add_send_kv_task(
        self,
        model_input: "ModelInputForGPUWithSamplingMetadata",
        kv_caches: List[torch.Tensor],
    ) -> None:
        return

    def add_place_kv_task(
        self,
        request_id: str,
        slot_mapping: List[int],
    ) -> None:
        return

    def query_received(self, request_id: str) -> bool:
        return True

    def close(self) -> None:
        pass


class CppConnector(ConnectorBase):
    """使用 CPP 扩展的传输"""

    """连接器, 用于连接 vllm 和 KV Buffer."""

    def __init__(
        self,
        worker_rank: int,
        local_rank: int,
        vllm_config: VllmConfig,
    ):
        """初始化流程:
        - 保存和分析配置
        - 构造 KV Cache Buffer
        - 构造通信控制变量
        """

        import kv_buffer

        assert vllm_config.kv_transfer_config.is_kv_transfer_instance

        self.worker_rank: int = worker_rank  # TP worker ID (buffer ID)
        self.local_rank: int = local_rank  # GPU device ID
        self.kv_rank: int = vllm_config.kv_transfer_config.kv_rank  # vllm instance ID

        self.vllm_config: VllmConfig = vllm_config
        self.transfer_config: KVTransferConfig = vllm_config.kv_transfer_config
        self.parallel_config: ParallelConfig = vllm_config.parallel_config

        # The size of a block (vllm page) in bytes
        self.block_size_bytes = self._get_block_size_bytes(
            self.vllm_config.cache_config,
            self.vllm_config.model_config,
            self.vllm_config.parallel_config,
        )

        logger.info(
            "[Connector] Initializing PyNcclConfig under kv_transfer_config %s",
            self.transfer_config,
        )

        # KV Buffer: 用于暂存和收发 KV Cache
        self.kv_cache_buffer = kv_buffer.KvBuffer(
            self.transfer_config.kv_ip,
            self.transfer_config.kv_port,
            self.kv_rank,
            self.transfer_config.kv_parallel_size,
            self.worker_rank,
            self.parallel_config.tensor_parallel_size,
            self.local_rank,
            int(self.transfer_config.kv_buffer_size),
            int(self.transfer_config.kv_buffer_size),
        )

        self.layer_info: Optional[LayerInfo] = None
        self.kv_caches: Optional[List[torch.Tensor]] = None

        logger.debug(f"[Connector] initialized for {self.kv_rank}")

    def init_model_info(self, model: torch.nn.Module) -> None:
        logger.debug("[Connector] Initializing model info for KV Connector")
        self.layer_info = LayerInfo(model)
        kv_cache_dtypes = [layer[0] for layer in self.layer_info.layer_attn]
        k_scales = [layer[1] for layer in self.layer_info.layer_attn]
        v_scales = [layer[2] for layer in self.layer_info.layer_attn]

        # 融合算子仅支持相同的 dtype 和 scale
        assert all(
            kv_cache_dtypes[0] == kv_cache_dtype for kv_cache_dtype in kv_cache_dtypes
        )
        assert all(k_scales[0] == k_scale for k_scale in k_scales)
        assert all(v_scales[0] == v_scale for v_scale in v_scales)

        logger.debug(f"[Connector] Layer info: {self.layer_info}")

    def init_kv_caches(self, kv_caches: List[List[torch.Tensor]]) -> None:
        logger.debug("[Connector] Initializing KV Cache for KV Connector")
        self.kv_caches: List[torch.Tensor] = kv_caches[0]  # 假设: 没有启用 pp
        # kv_caches: num_layers * [2, num_blocks, block_size, num_heads, head_size]
        self.key_cache_layer_ptrs = torch.tensor(
            [kv_cache_layer[0].data_ptr() for kv_cache_layer in self.kv_caches],
            dtype=torch.int64,
            device=self.kv_caches[0].device,
        )
        self.value_cache_layer_ptrs = torch.tensor(
            [kv_cache_layer[1].data_ptr() for kv_cache_layer in self.kv_caches],
            dtype=torch.int64,
            device=self.kv_caches[0].device,
        )

        self.block_size = self.kv_caches[0].size(2)
        self.block_stride = self.kv_caches[0].stride(1)

        assert self.layer_info is not None, "Layer info should be initialized first"

        logger.debug(
            f"[Connector] kv_cache shape of one layer: {self.kv_caches[0].shape}"
        )
        logger.debug(
            f"[Connector] block_size: {self.block_size}, block_stride: {self.block_stride}"
        )
        logger.debug(f"[Connector] key_cache_layer_ptrs: {self.key_cache_layer_ptrs}")

        placer_info = PlacerInfo(
            key_cache_layer_ptrs=self.key_cache_layer_ptrs,
            value_cache_layer_ptrs=self.value_cache_layer_ptrs,
            block_size=self.block_size,
            block_stride=self.block_stride,
            kv_cache_dtype=self.layer_info.layer_attn[0][0],
            k_scale=self.layer_info.layer_attn[0][1],
            v_scale=self.layer_info.layer_attn[0][2],
        )

        model_config = self.vllm_config.model_config
        parallel_config = self.vllm_config.parallel_config

        num_attention_layers = model_config.get_num_attention_layers(parallel_config)
        num_heads = model_config.get_num_kv_heads(parallel_config)
        head_size = model_config.get_head_size()

        self.kv_cache_buffer.init_placer_info(
            placer_info.key_cache_layer_ptrs,
            placer_info.value_cache_layer_ptrs,
            placer_info.block_size,
            placer_info.block_stride,
            placer_info.kv_cache_dtype,
            placer_info.k_scale,
            placer_info.v_scale,
            num_attention_layers,
            num_heads,
            head_size,
        )

        logger.debug("[Connector] KV Cache for buffer initialized")

    def can_buffer(self, num_blocks_required: int) -> bool:
        return self.kv_cache_buffer.can_send(num_blocks_required)

    def add_send_kv_task(
        self,
        model_input: "ModelInputForGPUWithSamplingMetadata",
        kv_caches: List[torch.Tensor],
    ) -> None:
        """发送 KV Cache  到指定实例 (后台异步执行).
        实现步骤:
        对 Batch 中的每个请求:
        - 检查是否需要发送
        - 如需要, 准备待发送项 (K, V)
            - 从 kv_caches 中取出待发送项 (从 model_input 寻找)
        - 将待发送项加入 buffer (后台会异步发送)
        """

        # batch 信息
        request_ids = model_input.request_ids
        ctrl_send_targets = model_input.ctrl_send_targets
        seq_lens = model_input.attn_metadata.seq_lens
        prompt_lens = model_input.prompt_lens
        ctrl_should_send_kvs = model_input.ctrl_should_send_kvs

        logger.debug(f"[Connector Sender] request_ids: {request_ids}")
        logger.debug(f"[Connector Sender] ctrl_should_send_kvs: {ctrl_should_send_kvs}")

        send_tasks: list[tuple[str, int, torch.Tensor]] = []
        for idx, request_id in enumerate(request_ids):
            if ctrl_should_send_kvs[idx] == False:
                continue  # 跳过没有发送标志的请求
            if seq_lens[idx] < prompt_lens[idx]:
                continue  # 跳过 prefill 未完成的请求 (chunked prefill)

            # 从 kv_caches 中取出待发送项
            # kv = self._fetch_kv_from_block_table(
            #     model_input,
            #     idx,
            #     kv_caches,
            # )
                        
            # (k/v, num_layers, num_tokens, num_heads, head_size)
            # (2, 80, 1, 8, 128)
            kv = torch.zeros((2, 80, 1, 8, 128), device=kv_caches[0].device)

            ctrl_send_target = ctrl_send_targets[idx]
            logger.debug(
                f"[Connector] Preparing sending item for {request_id}: {self.kv_rank} -> {ctrl_send_target}."
            )

            send_tasks.append((request_id, ctrl_send_target, kv))

        # 同步
        torch.cuda.current_stream().synchronize()

        for request_id, ctrl_send_target, kv in send_tasks:
            # 添加到 buffer (异步发送)
            self.kv_cache_buffer.add_send_task(request_id, ctrl_send_target, kv)
        del send_tasks

    def add_place_kv_task(self, request_id: str, slot_mapping: List[int]):
        """实现步骤:
        1. 构造 callback
        2. 传递给 buffer
        """
        assert self.layer_info is not None
        assert self.kv_caches is not None

        self.kv_cache_buffer.add_place_task(request_id, slot_mapping)

    def query_received(self, request_id: str) -> bool:
        return self.kv_cache_buffer.query_place_done(request_id)

    # ---- Private Methods ----

    def _build_req_slot_mapping(
        self,
        model_input: "ModelInputForGPUWithSamplingMetadata",
        idx: int,
    ) -> torch.Tensor:
        """构建请求完整的 slot_mapping
        Block table 概念: 从 0 索引的一维数组 (每个 token 占据 1 slot)
        slot mapping 概念: 用于指向当前 token list 在 Block table 中的实际存储地址. (kv cache 指针)
        实现步骤:
        - 拼接 Block Table 中所有 Block 的 slot id
        - 限制长度为 seq_len
        - 返回值依照 curr_slot_mapping 格式
        """

        seq_len = model_input.attn_metadata.seq_lens[idx]
        block_table = model_input.attn_metadata.block_tables[idx]

        block_size = self.vllm_config.cache_config.block_size
        assert block_table.numel() > 0

        # 计算每个 block 的起始和结束位置
        start_indices = block_table * block_size
        # 创建一个范围张量，表示每个 block 的 slot 范围
        slot_ranges = torch.arange(block_size, device="cuda").unsqueeze(
            0
        ) + start_indices.unsqueeze(1)
        # 将 slot_ranges 展平并截取前 seq_len 个元素
        req_slot_mapping = slot_ranges.flatten()[:seq_len]

        return req_slot_mapping

    def _fetch_kv_from_block_table(
        self,
        model_input: "ModelInputForGPUWithSamplingMetadata",
        idx: int,
        kv_caches: List[torch.Tensor],
    ) -> torch.Tensor:
        # model 信息
        start_layer = self.layer_info.start_layer
        end_layer = self.layer_info.end_layer

        # 为了 chunked_prefill/decode 能传输全部的 KV Cache, 需要提供完整的 slot_mapping
        current_slot_mapping = self._build_req_slot_mapping(model_input, idx)

        # 按层合并 KV Cache
        keys, values = [], []
        for layer_id in range(start_layer, end_layer):
            kv_cache = kv_caches[layer_id - start_layer]

            _, _, num_heads, head_size = kv_cache[0].shape

            # (num_tokens, num_heads, head_size)
            key_cache = kv_cache[0].reshape(-1, num_heads, head_size)
            value_cache = kv_cache[1].reshape(-1, num_heads, head_size)

            # (num_layers, num_tokens, num_heads, head_size)
            keys.append(key_cache[current_slot_mapping].unsqueeze(0))
            values.append(value_cache[current_slot_mapping].unsqueeze(0))

        num_layer = end_layer - start_layer
        combined_kv = keys + values

        # (k/v, num_layers, num_tokens, num_heads, head_size)
        kv = (
            torch.cat(combined_kv, dim=0)
            .reshape(2, num_layer, -1, num_heads, head_size)
            .contiguous()
        )

        return kv

    def _get_block_size_bytes(
        self,
        cache_config: CacheConfig,
        model_config: ModelConfig,
        parallel_config: ParallelConfig,
    ) -> int:
        head_size = model_config.get_head_size()
        num_heads = model_config.get_num_kv_heads(parallel_config)
        num_attention_layers = model_config.get_num_attention_layers(parallel_config)

        key_cache_block = cache_config.block_size * num_heads * head_size
        value_cache_block = key_cache_block
        total = num_attention_layers * (key_cache_block + value_cache_block)

        if cache_config.cache_dtype == "auto":
            dtype = model_config.dtype
        else:
            dtype = STR_DTYPE_TO_TORCH_DTYPE[cache_config.cache_dtype]
        dtype_size = get_dtype_size(dtype)

        # 输出 token size in MiB (用于性能分析)
        token_size_bytes = head_size * num_heads * dtype_size * num_attention_layers * 2
        token_size_MiB = token_size_bytes / 1024 / 1024
        logger.debug(
            f"[Connector] TOKEN_SIZE for current parallel config: {token_size_MiB:.2f} MiB"
        )

        # 返回 block size in bytes
        return dtype_size * total

    def close(self):
        self.kv_cache_buffer.close()
