"""A centralized entrypoint to perform distributed KV cache transfer."""

from typing import TYPE_CHECKING, List, Tuple, Union

if TYPE_CHECKING:
    from vllm.worker.model_runner import ModelInputForGPUWithSamplingMetadata
    from vllm.config import VllmConfig

import torch

from vllm.logger import init_logger
from vllm.sequence import IntermediateTensors

logger = init_logger(__name__)


class KVTransferAgent:
    """
    A class designated for distributed KV transfer

    Target use cases:
        1. Disaggregated prefill
        2. Remote KV cache storage

    """

    def __init__(
        self,
        rank: int,
        local_rank: int,
        vllm_config: "VllmConfig",
    ):
        assert (
            vllm_config.kv_transfer_config is not None
        ), "KVTransferConfig is not set in the VllmConfig, cannot initialize KVConnector."
        assert (
            vllm_config.kv_transfer_config.is_kv_transfer_instance
        ), "KVTransferAgent should only be used when kv_connector is set."

        self.vllm_config = vllm_config

        # 避免循环依赖
        from vllm.distributed.kv_transfer.kv_connector.simple_connector import (
            ConnectorBase,
            SimpleConnector,
            StubConnector,
            CppConnector,
        )

        SelectedConnector = StubConnector
        self.connector = SelectedConnector(rank, local_rank, vllm_config)

    def init_model_info(self, model: torch.nn.Module) -> None:
        self.connector.init_model_info(model)

    def init_kv_caches(self, kv_caches: List[List[torch.Tensor]]) -> None:
        return self.connector.init_kv_caches(kv_caches)

    def can_buffer(self, num_blocks_required: int) -> bool:
        return self.connector.can_buffer(num_blocks_required)

    def add_send_kv_task(
        self,
        model_input: "ModelInputForGPUWithSamplingMetadata",
        kv_caches: List[torch.Tensor],
    ) -> None:
        self.connector.add_send_kv_task(model_input, kv_caches)

    def query_received(self, request_id: str) -> bool:
        return self.connector.query_received(request_id)

    def add_place_kv_task(
        self,
        request_id: str,
        slot_mapping: List[int],
    ):
        self.connector.add_place_kv_task(request_id, slot_mapping)

    def close(self) -> None:
        self.connector.close()
