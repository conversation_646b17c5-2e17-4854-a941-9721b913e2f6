### 与具体实验无关的通用配置

# 可选模型
models: # List[Tu<PERSON>[MODEL, MODEL_LEN]]
  - ["Qwen/Qwen2.5-1.5B", 16384] #0
  - ["Qwen/Qwen2.5-14B", 16384] #1
  - ["Qwen/Qwen2.5-32B", 16384] #2
  - ["Qwen/Qwen2.5-72B", 16384] #3
  - ["meta-llama/Meta-Llama-3-8B", 16384] #4
  - ["meta-llama/Meta-Llama-3-70B", 16384] #5
  - ["01-ai/Yi-1.5-34B", 4096] #6
  - ["meta-llama/Llama-2-13b-hf", 4096] #7


# 可选数据集
datasets: # List[Tuple[DATASET_NAME, List[RAW_DATASET_FILE], FILTERED_DATASET_FILE]]
  - ["dummy", [], "dummy.ds", 4096] #0
  - ["longbench", ["raw/longbench"], "longbench.ds", 16384] #1
  - ["sharegpt", ["raw/ShareGPT_V3_unfiltered_cleaned_split.json"], "sharegpt.ds", 2048] #2
  - ["mixed", ["sharegpt.ds", "longbench.ds"], "mixed.ds", 4096] #3
  - ["arxiv", ["raw/arxiv.parquet"], "arxiv.ds", 16384] #4 
  - ["openchat", ["raw/openchat_8192.train.text.json"], "openchat.ds", 4096] #5
  - ["humaneval", ["raw/HumanEval.jsonl"], "humaneval.ds", 2048] #6
  - ["openthoughts", ["raw/train-00000-of-00006.parquet"], "openthoughts.ds", 4096] #7


# 环境变量
env_vars: 
  - "HF_HUB_OFFLINE=1"
  - "VLLM_LOGGING_LEVEL=DEBUG"
  # - "VLLM_LOGGING_LEVEL=INFO"
  - "NCCL_DEBUG=INFO"
  - "OMP_NUM_THREADS=1"
  - "MKL_NUM_THREADS=1"
  # - "CUDA_LAUNCH_BLOCKING=1"
  

# 端口配置
proxy_port: 16449
instance_ports: [16450, 16451, 16452, 16453, 16454, 16455, 16456, 16457]


# 平台环境相关的配置 (包括路径, 可用显卡, GPU 利用率)
platforms: 
  "cwang-X99": # X99 双卡 3060 平台
    python_path: "/home/<USER>/miniforge3/envs/PD-vllm/bin/python"
    dataset_dir: "/home/<USER>/dataset"
    gpu_devices: [0, 1]
    kv_buffer_size: "1e9"
    gpu_memory_utilization: "0.8"
  
  "992cdc39c3cc": # 5 卡 4090 平台
    python_path: "/opt/conda/envs/PD-vllm/bin/python"
    dataset_dir: "/root/dataset"
    gpu_devices: [4, 3, 2, 1, 0]
    kv_buffer_size: "2e9"
    gpu_memory_utilization: "0.8"

  "a4c2f3c1e448": #  8 卡 H20 平台
    python_path: "/root/miniconda3/envs/PD-vllm/bin/python"
    dataset_dir: "/root/dataset"
    gpu_devices: [7, 6, 5, 4, 3, 2, 1, 0]
    kv_buffer_size: "4e9"
    gpu_memory_utilization: "0.7"
  
  "8bf5911e3ccf": # RunPod 4*A100
    python_path: "/root/miniforge3/envs/PD-vllm/bin/python"
    dataset_dir: "/root/dataset"
    gpu_devices: [0, 1, 2, 3, 4, 5, 6, 7]
    kv_buffer_size: "4e9"
    gpu_memory_utilization: "0.8"

  "6ce88b6e91ff": # 2387d
    python_path: "/root/miniforge3/envs/PD-vllm/bin/python"
    dataset_dir: "/root/dataset"
    gpu_devices: [0, 1, 2, 3, 4, 5, 6, 7]
    kv_buffer_size: "4e9"
    gpu_memory_utilization: "0.8"

  "cdb6030257f2": # 2386d
    python_path: "/root/miniforge3/envs/PD-vllm/bin/python"
    dataset_dir: "/root/dataset"
    gpu_devices: [0, 1, 2, 3, 4, 5, 6, 7]
    kv_buffer_size: "4e9"
    gpu_memory_utilization: "0.8"