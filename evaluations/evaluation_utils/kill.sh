#!/bin/bash

ulimit -n 1048576


# kill by service port
for port in {16449..16457}; do
    lsof -t -i :$port | xargs -r kill -9
done 


# kill by transfer port
for port in {14579..14611}; do
    lsof -t -i :$port | xargs -r kill -9
done 


# kill by process name (for TP sub-process)
pids=$(ps -ef | grep multiprocessing | grep -v grep | awk '{print $2}')
if [ -n "$pids" ]; then
    echo "$pids"
    kill -9 $pids
fi


