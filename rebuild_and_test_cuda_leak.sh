#!/bin/bash

# <PERSON>ript to rebuild C++ extension with CUDA memory debugging and test for leaks

set -e

echo "🔧 Rebuilding C++ Extension with CUDA Memory Debugging"
echo "====================================================="

# Navigate to extension directory
cd /root/PD-vllm/vllm/distributed/kv_transfer/transfer_ext

# Clean and rebuild
echo "🧹 Cleaning previous builds..."
rm -rf build/ dist/ *.egg-info/
find . -name "*.so" -delete

echo "🔨 Building C++ extension..."
python setup.py clean --all
python setup.py build_ext --inplace
python setup.py install --force

echo "✅ C++ extension rebuilt successfully!"

# Go back to root
cd /root/PD-vllm/

# Verify installation
python -c "import kv_buffer; print('✅ kv_buffer imported successfully')"

echo ""
echo "🔍 Running CUDA Memory Leak Detection..."
echo ""

# Run the CUDA memory leak detection
python cuda_memory_leak_pinpoint.py

echo ""
echo "📋 Summary of debugging features added:"
echo "  ✅ CUDA memory monitoring before/after tensor destruction"
echo "  ✅ Detailed memory tracking at each pipeline step"
echo "  ✅ PyTorch vs NVIDIA memory comparison"
echo "  ✅ Leak operation identification"
echo ""
echo "🔍 Look for these patterns in the output:"
echo "  1. [CUDA_DEBUG] BEFORE/AFTER TENSOR DESTRUCTION - shows CUDA memory changes"
echo "  2. Memory growth operations - identifies where leaks occur"
echo "  3. PyTorch vs NVIDIA memory differences - shows if PyTorch is tracking correctly"
echo ""
