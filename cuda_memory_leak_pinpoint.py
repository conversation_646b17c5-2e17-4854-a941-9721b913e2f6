#!/usr/bin/env python3
"""
CUDA Memory Leak Pinpoint Tool

This tool helps identify the exact location of CUDA memory leaks by:
1. Monitoring CUDA memory at each step of the KV transfer pipeline
2. Comparing memory before/after tensor operations
3. Identifying which specific operations are not releasing memory
4. Providing actionable insights for fixing the leak
"""

import torch
import gc
import time
import subprocess
import re
from typing import Dict, List, Tuple, Optional


class CUDAMemoryMonitor:
    """Precise CUDA memory monitoring for leak detection."""
    
    def __init__(self, device_id: int = 0):
        self.device_id = device_id
        self.device = torch.device(f"cuda:{device_id}")
        self.memory_log: List[Dict] = []
        
    def get_cuda_memory_detailed(self) -> Dict[str, float]:
        """Get detailed CUDA memory information."""
        if not torch.cuda.is_available():
            return {"allocated": 0, "reserved": 0, "free": 0}
        
        # PyTorch memory info
        allocated = torch.cuda.memory_allocated(self.device) / 1024**2
        reserved = torch.cuda.memory_reserved(self.device) / 1024**2
        
        # nvidia-smi memory info (more accurate for total usage)
        try:
            result = subprocess.run([
                'nvidia-smi', '--query-gpu=memory.used,memory.free,memory.total',
                '--format=csv,noheader,nounits', f'--id={self.device_id}'
            ], capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                used, free, total = map(float, result.stdout.strip().split(','))
                return {
                    "allocated_pytorch": allocated,
                    "reserved_pytorch": reserved,
                    "used_nvidia": used,
                    "free_nvidia": free,
                    "total_nvidia": total
                }
        except:
            pass
        
        # Fallback to PyTorch only
        props = torch.cuda.get_device_properties(self.device)
        total = props.total_memory / 1024**2
        
        return {
            "allocated_pytorch": allocated,
            "reserved_pytorch": reserved,
            "used_nvidia": reserved,  # Approximation
            "free_nvidia": total - reserved,
            "total_nvidia": total
        }
    
    def log_memory(self, context: str, details: str = ""):
        """Log current memory state with context."""
        memory_info = self.get_cuda_memory_detailed()
        
        log_entry = {
            "timestamp": time.time(),
            "context": context,
            "details": details,
            **memory_info
        }
        
        self.memory_log.append(log_entry)
        
        print(f"[CUDA_MONITOR] {context}: "
              f"PyTorch={memory_info['allocated_pytorch']:.1f}MB, "
              f"NVIDIA={memory_info['used_nvidia']:.1f}MB, "
              f"Free={memory_info['free_nvidia']:.1f}MB {details}")
        
        return log_entry
    
    def analyze_memory_leaks(self) -> Dict:
        """Analyze memory log to identify leak patterns."""
        if len(self.memory_log) < 2:
            return {"error": "Need at least 2 memory logs"}
        
        # Find operations that increased memory
        leak_operations = []
        
        for i in range(1, len(self.memory_log)):
            prev = self.memory_log[i-1]
            curr = self.memory_log[i]
            
            pytorch_growth = curr["allocated_pytorch"] - prev["allocated_pytorch"]
            nvidia_growth = curr["used_nvidia"] - prev["used_nvidia"]
            
            if pytorch_growth > 10 or nvidia_growth > 10:  # >10MB growth
                leak_operations.append({
                    "from_context": prev["context"],
                    "to_context": curr["context"],
                    "pytorch_growth_mb": pytorch_growth,
                    "nvidia_growth_mb": nvidia_growth,
                    "timestamp": curr["timestamp"]
                })
        
        # Calculate total growth
        first = self.memory_log[0]
        last = self.memory_log[-1]
        
        total_pytorch_growth = last["allocated_pytorch"] - first["allocated_pytorch"]
        total_nvidia_growth = last["used_nvidia"] - first["used_nvidia"]
        
        return {
            "total_pytorch_growth_mb": total_pytorch_growth,
            "total_nvidia_growth_mb": total_nvidia_growth,
            "leak_operations": leak_operations,
            "duration_seconds": last["timestamp"] - first["timestamp"],
            "total_operations": len(self.memory_log)
        }
    
    def print_leak_analysis(self):
        """Print detailed leak analysis."""
        analysis = self.analyze_memory_leaks()
        
        if "error" in analysis:
            print(f"❌ {analysis['error']}")
            return
        
        print("\n" + "="*60)
        print("🔍 CUDA MEMORY LEAK ANALYSIS")
        print("="*60)
        
        print(f"📊 Overall Memory Growth:")
        print(f"  PyTorch Allocated: {analysis['total_pytorch_growth_mb']:.1f}MB")
        print(f"  NVIDIA Used: {analysis['total_nvidia_growth_mb']:.1f}MB")
        print(f"  Duration: {analysis['duration_seconds']:.1f}s")
        print(f"  Total Operations: {analysis['total_operations']}")
        
        if analysis['leak_operations']:
            print(f"\n🚨 MEMORY LEAK OPERATIONS ({len(analysis['leak_operations'])} found):")
            for i, op in enumerate(analysis['leak_operations'][:10]):  # Show top 10
                print(f"  {i+1}. {op['from_context']} → {op['to_context']}")
                print(f"     PyTorch: +{op['pytorch_growth_mb']:.1f}MB, "
                      f"NVIDIA: +{op['nvidia_growth_mb']:.1f}MB")
        else:
            print(f"\n✅ No significant memory leak operations detected")
        
        # Identify the biggest leaks
        if analysis['leak_operations']:
            biggest_pytorch = max(analysis['leak_operations'], 
                                key=lambda x: x['pytorch_growth_mb'])
            biggest_nvidia = max(analysis['leak_operations'], 
                               key=lambda x: x['nvidia_growth_mb'])
            
            print(f"\n🎯 BIGGEST LEAKS:")
            print(f"  PyTorch: {biggest_pytorch['from_context']} → {biggest_pytorch['to_context']} "
                  f"(+{biggest_pytorch['pytorch_growth_mb']:.1f}MB)")
            print(f"  NVIDIA: {biggest_nvidia['from_context']} → {biggest_nvidia['to_context']} "
                  f"(+{biggest_nvidia['nvidia_growth_mb']:.1f}MB)")


def test_kv_operations_with_precise_monitoring():
    """Test KV operations with precise memory monitoring at each step."""
    print("🔍 Testing KV Operations with Precise CUDA Memory Monitoring")
    print("=" * 60)
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return False
    
    monitor = CUDAMemoryMonitor()
    monitor.log_memory("test_start", "Initial state")
    
    try:
        import kv_buffer
        
        # Step 1: Create KV buffer
        monitor.log_memory("before_buffer_creation")
        
        buffer = kv_buffer.KvBuffer(
            host="localhost",
            port=12345,
            rank=0,
            size=1,
            tp_rank=0,
            tp_size=1,
            device=0,
            send_buffer_size_threshold=1024*1024*50,  # 50MB
            recv_buffer_size_threshold=1024*1024*50
        )
        
        monitor.log_memory("after_buffer_creation")
        
        # Step 2: Create test tensor
        monitor.log_memory("before_tensor_creation")
        
        tensor_shape = (2, 16, 256, 16, 64)  # Smaller tensor for testing
        test_tensor = torch.randn(tensor_shape, device='cuda', dtype=torch.float16)
        tensor_size_mb = test_tensor.numel() * test_tensor.element_size() / 1024**2
        
        monitor.log_memory("after_tensor_creation", f"tensor_size={tensor_size_mb:.1f}MB")
        
        # Step 3: Test multiple KV operations
        num_operations = 15
        print(f"\n🔄 Testing {num_operations} KV operations with detailed monitoring...")
        
        for i in range(num_operations):
            request_id = f"test_req_{i}"
            
            # Monitor before add_send_task
            monitor.log_memory(f"before_add_task_{i}")
            
            # Add send task
            buffer.add_send_task(request_id, 0, test_tensor)
            
            # Monitor after add_send_task
            monitor.log_memory(f"after_add_task_{i}")
            
            # Wait for background processing
            time.sleep(0.3)
            
            # Monitor after processing
            monitor.log_memory(f"after_processing_{i}")
            
            # Periodic cleanup
            if i % 5 == 0:
                gc.collect()
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                monitor.log_memory(f"after_cleanup_{i}")
        
        # Step 4: Final cleanup
        monitor.log_memory("before_final_cleanup")
        
        del test_tensor
        buffer.close()
        
        monitor.log_memory("after_buffer_close")
        
        # Aggressive cleanup
        gc.collect()
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        
        monitor.log_memory("after_aggressive_cleanup")
        
        # Analyze results
        monitor.print_leak_analysis()
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        monitor.log_memory("error_state", f"error={str(e)}")
        monitor.print_leak_analysis()
        return False


def main():
    """Run the CUDA memory leak pinpoint test."""
    print("🚀 CUDA Memory Leak Pinpoint Tool")
    print("=" * 50)
    
    # Check CUDA availability
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return False
    
    print(f"✅ CUDA Device: {torch.cuda.get_device_name()}")
    print(f"📊 Total GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    
    # Run the test
    success = test_kv_operations_with_precise_monitoring()
    
    if success:
        print("\n🎉 Memory monitoring completed!")
        print("\n📝 Next Steps:")
        print("  1. Look for operations with significant memory growth")
        print("  2. Check if memory is released after tensor operations")
        print("  3. Compare PyTorch vs NVIDIA memory usage")
        print("  4. Focus on the biggest leak operations identified")
    else:
        print("\n⚠️  Memory monitoring encountered issues")
    
    return success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
