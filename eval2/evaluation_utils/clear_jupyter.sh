#!/bin/bash

# Usage: ./clean_ipynb_outputs.sh /path/to/directory

# Get the directory from the command line argument
DIR="$1"

# Check if a directory was provided
if [ -z "$DIR" ]; then
  echo "Please provide a directory."
  exit 1
fi

# Check if the provided argument is a directory
if [ ! -d "$DIR" ]; then
  echo "The provided argument is not a directory."
  exit 1
fi

# Iterate over all .ipynb files in the given directory
find "$DIR" -type f -name '*.ipynb' | while read -r file; do
  echo "Clearing output from: $file"
  # Use nbconvert to clear output
  jupyter nbconvert --to notebook --ClearOutputPreprocessor.enabled=True --inplace "$file"
done