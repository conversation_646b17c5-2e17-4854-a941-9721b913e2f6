import os
import sys
import yaml
import socket

from pydantic import BaseModel, model_validator
from typing import List, Dict, Any, Tuple, Optional
from matplotlib import pyplot as plt
import dataclasses
import json


from .conf_loader import load_conf
from abc import ABC, abstractmethod

"""实验流程:
1. yaml 定义配置
2. pydantic 读取载入配置, 生成配置对象
3. 根据配置对象生成脚本
4. 脚本执行
5. 提取 log 并绘图
"""

"""配置文件组织:
- common_conf.yaml: 通用配置, 如平台配置, 用于提取通用配置.
- 具体实验配置: 每个实验一个配置文件 (每个 model+dataset 对应一套配置)
"""

"""pydantic 组织:
每个子类型有单独的类
"""

"""ScriptGenerator 组织:
辅助函数:
- 为脚本准备路径信息 (创建并保存路径)

生成函数:
- 总生成函数
    - 生成一个 QPS 对应的脚本
        - 生成一个调度器对应的脚本
            - 生成启动脚本
                - 生成 proxy 和 instance 启动脚本
                - 生成等待命令
            - 生成 Benchmark 脚本
            - 生成结束脚本
"""

"""ScriptGenerator: 根据 conf 生成良好组织的运行脚本, 具有如下特点:
- 能根据不同的 conf 生成不同的脚本
- 能以正确的架构组织脚本和 log
"""

"""路径组织:
文件类型缓存路径: .cache/{script or log}/
特定实验的路径命名规则: {文件类型缓存路径}/{model}/{dataset}/{qps}/{scheduler}/{script_file or log_file}
"""


class ModelConf(BaseModel):
    model_info: List[Any]


class DatasetConf(BaseModel):
    dataset_info: List[Any]


class SchedulerConf(BaseModel):
    decoding_size: int
    chunk_sizes: List[int]
    group_sizes: List[int]
    cli_group_sizes: str
    enable_flowing: str
    flowing_watermark: float
    schedule_policy: str
    enable_zero_decoding: str
    prefill_token_limit: List[int]
    cli_prefill_token_limit: str  # 自动生成
    decoding_policy: str
    disable_async_output: list[bool]
    prefill_processing_capacities: list[float]

    @model_validator(mode="before")
    def extract_cli_prefill_token_limit(cls, values):
        values["cli_prefill_token_limit"] = " ".join(
            map(str, values["prefill_token_limit"])
        )
        return values

    @model_validator(mode="before")
    def extract_cli_group_sizes(cls, values):
        values["cli_group_sizes"] = " ".join(map(str, values["group_sizes"]))
        print(values["chunk_sizes"], values["group_sizes"], values["cli_group_sizes"])
        return values


class EvaluationConf(BaseModel):
    # 环境配置
    python_path: str
    dataset_dir: str
    gpu_devices: List[int]
    kv_buffer_size: str
    gpu_memory_utilization: float
    watermark: float
    load_aware_buffer_length: int
    env_vars: List[str]
    cli_env: str  # 自动生成

    # 模型配置
    selected_model: int
    models: List[Tuple[str, int]]
    model: str  # 自动生成
    model_len: int  # 自动生成
    world_size: int
    tp_size: int
    cli_tp_group: List[str]  # 自动生成
    proxy_port: int
    instance_ports: List[int]
    cli_instance_ports: str  # 自动生成

    # 数据集配置
    selected_dataset: int
    datasets: List[tuple[str, List[str], str, int]]
    dataset: str  # 自动生成
    dataset_file: str  # 自动生成
    qps_list: List[float]
    num_prompts: int

    # 指标配置
    ttft_slo: float
    tpot_slo: float
    steady_state_start: float
    steady_state_end: float
    slo_scale_qps: float
    slo_scale_list: list[float]
    slo_dict: Dict[str, tuple[float, float]]

    # 调度器配置
    schedulers: Dict[str, SchedulerConf]

    @model_validator(mode="before")
    def extract_model_dataset(cls, values):
        values["model"], values["model_len"] = values["models"][
            values["selected_model"]
        ]
        values["dataset"], _, values["dataset_file"], _ = values["datasets"][
            values["selected_dataset"]
        ]
        return values

    @model_validator(mode="before")
    def validate_group_size(cls, values):
        # 检查 group_sizes 和 chunk_sizes 是否一致
        if "hybrid" in values["schedulers"]:
            assert values["world_size"] == sum(
                values["schedulers"]["hybrid"]["group_sizes"]
            ), f"world_size {values['world_size']} does not match group sizes {values['schedulers']['hybrid']['group_sizes']}"

        return values

    @model_validator(mode="before")
    def extract_cli_env(cls, values):
        values["cli_env"] = " ".join(values["env_vars"])
        return values

    @model_validator(mode="before")
    def extract_cli_instance_ports(cls, values):
        values["cli_instance_ports"] = " ".join(map(str, values["instance_ports"]))
        return values

    @model_validator(mode="before")
    def extract_cli_tp_group(cls, values):
        assert values["world_size"] * values["tp_size"] <= len(
            values["gpu_devices"]
        ), f"Not enough devices for all instances! Check configuration."
        tp_groups = [
            values["gpu_devices"][i : i + values["tp_size"]]
            for i in range(0, len(values["gpu_devices"]), values["tp_size"])
        ]
        cli_tp_groups = [",".join(map(str, group)) for group in tp_groups]
        values["cli_tp_group"] = cli_tp_groups
        return values


def load_evaluation_conf(common_conf_path: str, conf_path: str) -> EvaluationConf:
    """载入配置文件, 生成配置对象"""
    final_conf = load_conf(common_conf_path, conf_path)

    return EvaluationConf(**final_conf)


class ScriptMaker:
    """脚本生成类
    - 初始化时提供脚本目录和脚本名,
    - 调用 add_command 添加一条命令, 填充命令的内容 (env, cmd, args, log, bg)
    - 退出时将所有命令写入脚本文件
    """

    def __init__(self, script_path, is_explicit=False):
        self.script_path = script_path

        # 脚本的命令列表
        self.commands = [
            "#!/bin/bash",
            "ulimit -n 1048576",
        ]
        if is_explicit:
            self.commands.append("set -x")

    def add_command(
        self,
        env: str = "",  # 环境变量
        cmd: str = "",  # 命令本体
        args: list[str] | str = "",  # 参数列表
        log: str = "",  # log 路径
        bg: bool = False,  # 是否后台运行
    ):
        command_parts = []

        # 添加环境变量
        if env:
            command_parts.append(env)

        # 添加命令本体
        assert cmd, "Command must be provided"
        command_parts.append(cmd)

        # 处理参数
        if args:
            if isinstance(args, str):
                command_parts.append(args)
            else:
                command_parts.extend(args)

        # 是否将输出写入 log
        if log:
            command_parts.append(f"> {log} 2>&1")

        # 是否后台运行
        if bg:
            command_parts.append("&")

        # 为了可读性, 命令的每个部分单独一行
        command = " \\\n".join(command_parts)
        self.commands.append(command)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        with open(self.script_path, "w") as script_file:
            for command in self.commands:
                script_file.write(command + "\n\n")  # 双换行分隔命令


def generate_wait_script(
    ports: list[int], kill_script_path: str, timeout: int = 120
) -> str:
    ports_str = " ".join(map(str, ports))
    shell_script = f"""
wait_for_servers() {{
    # wait for all instances to start
    # return 1 if any instance fails to start
    local ports=({ports_str})
    for port in "${{ports[@]}}"; do
        # echo "Waiting for server on port $port..."
        timeout {timeout} bash -c "
            until curl -s localhost:${{port}}/v1/completions > /dev/null; do
                sleep 1
            done" || {{
                echo "Server on port $port failed to start."
                return 1
            }}
    done
    # echo "All servers started successfully."
    return 0
}}

cleanup() {{
    bash {kill_script_path}
}}

# Main script logic
if ! wait_for_servers; then
    echo "One or more servers failed to start. Cleaning up..."
    cleanup
    exit 1
fi
exit 0
"""
    return shell_script


def generate_kill_script(proxy_port: int, world_size: int, kv_port) -> str:
    shell_script = f"""
# kill by service port
for port in {{{proxy_port}..{proxy_port+world_size+1}}}; do
    lsof -t -i :$port | xargs -r kill -9
done \n

# kill by transfer port
for port in {{{kv_port}..{kv_port+world_size*4}}}; do
    lsof -t -i :$port | xargs -r kill -9
done \n

# kill by process name (for TP sub-process)
pids=$(ps -ef | grep multiprocessing | grep -v grep | awk '{{print $2}}')
if [ -n "$pids" ]; then
    echo "$pids"
    kill -9 $pids
fi
"""
    return shell_script


class ScriptGenerator:
    """脚本生成器: 读取配置, 生成脚本"""

    def __init__(self, conf: EvaluationConf):
        self.conf = conf
        self.cur_dir = os.getcwd()

    def prepare_path(
        self,
        model: str,
        dataset: str,
        qps: Optional[float] = None,
        scheduler: Optional[str] = None,
    ) -> Tuple[str, str]:
        """返回 script_dir, log_dir, 如果没有则创建
        script_dir: .cache/{model}/{dataset}/[{qps}]/[{scheduler}]
        log_dir: .cache/{model}/{dataset}/[{qps}]/[{scheduler}]
        """
        cache_dir = os.path.join(self.cur_dir, ".cache")
        eval_dir = os.path.join(cache_dir, model, dataset)
        if qps:
            eval_dir = os.path.join(eval_dir, f"{qps:.2f}")
        if scheduler:
            eval_dir = os.path.join(eval_dir, scheduler)

        script_dir = os.path.join(eval_dir, "scripts")
        log_dir = os.path.join(eval_dir, "logs")

        for dir in [script_dir, log_dir]:
            os.makedirs(dir, exist_ok=True)

        return script_dir, log_dir

    def generate_all_scripts(self) -> str:

        cmds: List[str] = []
        for qps in self.conf.qps_list:
            cmd = self.generate_qps_script(qps)
            cmds.append(cmd)

        script_dir, log_dir = self.prepare_path(self.conf.model, self.conf.dataset)
        script_path = os.path.join(script_dir, "evaluate_all_qps.sh")
        with ScriptMaker(script_path) as sm:
            for cmd in cmds:
                sm.add_command(cmd=f"bash {cmd}")

        return script_path

    def generate_qps_script(self, qps: float) -> str:
        cmds: List[str] = []
        for scheduler, scheduler_conf in self.conf.schedulers.items():
            cmd = self.generate_scheduler_script(qps, scheduler, scheduler_conf)
            cmds.append(cmd)

        script_dir, log_dir = self.prepare_path(self.conf.model, self.conf.dataset, qps)
        script_path = os.path.join(script_dir, f"evaluate_qps.sh")
        with ScriptMaker(script_path) as sm:
            for cmd in cmds:
                sm.add_command(cmd=f"bash {cmd}")
        return script_path

    def generate_scheduler_script(
        self, qps: float, scheduler: str, scheduler_conf: SchedulerConf
    ):
        start_script = self.generate_start_script(qps, scheduler, scheduler_conf)
        bench_script = self.generate_benchmark_script(qps, scheduler, scheduler_conf)
        end_script = self.generate_end_script(qps, scheduler, scheduler_conf)

        script_dir, log_dir = self.prepare_path(self.conf.model, self.conf.dataset, qps)
        script_path = os.path.join(script_dir, f"evaluate_{scheduler}.sh")
        with ScriptMaker(script_path, is_explicit=True) as sm:
            sm.add_command(cmd=f"bash {start_script}")
            sm.add_command(cmd=f"bash {bench_script}")
            sm.add_command(cmd=f"bash {end_script}")
        return script_path

    def generate_start_script(
        self, qps: float, scheduler: str, scheduler_conf: SchedulerConf
    ) -> str:
        script_dir, log_dir = self.prepare_path(
            self.conf.model, self.conf.dataset, qps, scheduler
        )
        script_path = os.path.join(script_dir, "start.sh")
        with ScriptMaker(script_path) as sm:
            # 生成 proxy
            script = self.generate_proxy_script(qps, scheduler, scheduler_conf)
            sm.add_command(cmd=f"bash {script}")
            # 生成 instance
            for i in range(self.conf.world_size):
                script = self.generate_instance_script(
                    qps, scheduler, scheduler_conf, i
                )
                sm.add_command(cmd=f"bash {script}")
            # 生成等待命令
            script = self.generate_wait_script(qps, scheduler, scheduler_conf)
            sm.add_command(cmd=f"bash {script}")
        return script_path

    def generate_benchmark_script(
        self, qps: float, scheduler: str, scheduler_conf: SchedulerConf
    ) -> str:
        script_dir, log_dir = self.prepare_path(
            self.conf.model, self.conf.dataset, qps, scheduler
        )
        script_path = os.path.join(script_dir, "benchmark.sh")
        log_path = os.path.join(log_dir, "benchmark.log")
        with ScriptMaker(script_path) as sm:
            # 生成 Benchmark 脚本
            sm.add_command(
                env=self.conf.cli_env,
                cmd=f"{self.conf.python_path} -m benchmarks.benchmark_serving",
                args=[
                    f"--backend vllm",
                    f"--ignore-eos",
                    f"--model {self.conf.model}",
                    f"--dataset-name {self.conf.dataset}",
                    f"--dataset-path {os.path.join(self.conf.dataset_dir, self.conf.dataset_file)}",
                    f"--request-rate {qps}",
                    f"--num-prompts {self.conf.num_prompts}",
                    f"--steady-state-start {self.conf.steady_state_start}",
                    f"--steady-state-end {self.conf.steady_state_end}",
                    f"--port {self.conf.proxy_port}",
                ],
                log=log_path,
            )
        return script_path

    def generate_end_script(
        self, qps: float, scheduler: str, scheduler_conf: SchedulerConf
    ) -> str:
        script_dir, log_dir = self.prepare_path(
            self.conf.model, self.conf.dataset, qps, scheduler
        )
        script_path = os.path.join(script_dir, "end.sh")
        with ScriptMaker(script_path) as sm:
            sm.add_command(
                cmd=generate_kill_script(
                    self.conf.proxy_port, self.conf.world_size, 14579
                )
            )

        return script_path

    def generate_proxy_script(
        self, qps: float, scheduler: str, scheduler_conf: SchedulerConf
    ) -> str:
        script_dir, log_dir = self.prepare_path(
            self.conf.model, self.conf.dataset, qps, scheduler
        )
        script_path = os.path.join(script_dir, "proxy.sh")
        log_path = os.path.join(log_dir, "proxy.log")
        with ScriptMaker(script_path) as sm:
            # 生成 proxy 脚本
            sm.add_command(
                env=self.conf.cli_env,
                cmd=f"{self.conf.python_path} -m vllm.entrypoints.openai.distributed_proxy",
                args=[
                    f"--model {self.conf.model}",
                    f"--world_size {self.conf.world_size}",
                    f"--decoding_size {scheduler_conf.decoding_size}",
                    f"--proxy_port {self.conf.proxy_port}",
                    f"--instance_ports {self.conf.cli_instance_ports}",
                    f"--scheduler_type {scheduler}",
                    f"--schedule_policy {scheduler_conf.schedule_policy}",
                    f"{scheduler_conf.enable_zero_decoding}",
                    f"--load_aware_buffer_length {self.conf.load_aware_buffer_length}",
                    f"--prefill_token_limit {scheduler_conf.cli_prefill_token_limit}",
                    f"--decoding_policy {scheduler_conf.decoding_policy}",
                    (
                        f"--group_sizes {scheduler_conf.cli_group_sizes}"
                        if scheduler_conf.cli_group_sizes
                        else ""
                    ),
                    f"--slo_dict '{json.dumps(self.conf.slo_dict)}'",
                    f"--prefill_processing_capacities '{json.dumps(scheduler_conf.prefill_processing_capacities)}'",
                ],
                log=log_path,
                bg=True,
            )
        return script_path

    def generate_instance_script(
        self, qps: float, scheduler: str, scheduler_conf: SchedulerConf, i: int
    ) -> str:
        script_dir, log_dir = self.prepare_path(
            self.conf.model, self.conf.dataset, qps, scheduler
        )
        script_path = os.path.join(script_dir, f"instance_{i}.sh")
        log_path = os.path.join(log_dir, f"instance_{i}.log")
        with ScriptMaker(script_path) as sm:
            # 生成 instance 脚本
            enable_chunked_prefill = scheduler_conf.chunk_sizes[i] > 0
            chunk_size = (
                scheduler_conf.chunk_sizes[i]
                if enable_chunked_prefill
                else self.conf.model_len
            )
            sm.add_command(
                env=f"{self.conf.cli_env} CUDA_VISIBLE_DEVICES={self.conf.cli_tp_group[i]}",
                cmd=f"{self.conf.python_path} -m vllm.entrypoints.openai.api_server",
                args=[
                    f"--model {self.conf.model}",
                    f"--port {self.conf.instance_ports[i]}",
                    f"--tensor_parallel_size {self.conf.tp_size}",
                    f"--max_model_len {self.conf.model_len}",
                    f"{'--enable-chunked-prefill' if enable_chunked_prefill else ''}",
                    f"--max_num_seqs {min(384, chunk_size)}",  # 避免 CUDA Graph 采集过多
                    f"--max_num_batched_tokens {chunk_size}",
                    f"--gpu-memory-utilization {self.conf.gpu_memory_utilization}",
                    f"--watermark {self.conf.watermark}",
                    f"{scheduler_conf.enable_flowing}",
                    f"--flowing_watermark {scheduler_conf.flowing_watermark}",
                    f"--load-format dummy",
                    f"--enforce-eager",
                    # f"{'--disable-async-output-proc' if scheduler_conf.disable_async_output[i] else ''}",  # 禁止异步处理, 避免额外 Iteration
                    f"--kv-transfer-config",
                    f' \'{{"kv_connector":"PyNcclConnector","kv_role":"kv_both","kv_rank":{i},"kv_parallel_size":{self.conf.world_size},"kv_buffer_size":{self.conf.kv_buffer_size}}}\' ',
                ],
                log=log_path,
                bg=True,
            )
        return script_path

    def generate_wait_script(
        self, qps: float, scheduler: str, scheduler_conf: SchedulerConf
    ) -> str:
        script_dir, log_dir = self.prepare_path(
            self.conf.model, self.conf.dataset, qps, scheduler
        )
        script_path = os.path.join(script_dir, f"wait.sh")
        with ScriptMaker(script_path) as sm:
            # 等待脚本
            cmd = generate_wait_script(
                [self.conf.proxy_port]
                + self.conf.instance_ports[0 : self.conf.world_size],
                os.path.join(script_dir, "end.sh"),
                120,
            )
            sm.add_command(cmd=cmd)
        return script_path


@dataclasses.dataclass
class Backend:
    name: str
    label: str
    color: str
    marker: str = "o"
    zorder: int = 0


class Plotter(ABC):
    """画图基类.
    汇聚通用的基本功能. 具体图片的样式和内容由子类实现.
    共用接口: plot()
    """

    def __init__(self, conf: EvaluationConf):
        self.conf = conf
        self.cur_dir = os.getcwd()
        if not os.path.exists(".cache"):
            raise FileNotFoundError(
                "No cache directory found. Please run evaluation first."
            )

        self.evaluation_dir = os.path.join(
            self.cur_dir, ".cache", self.conf.model, self.conf.dataset
        )
        self.figure_dir = os.path.join(self.evaluation_dir, "figures")
        if not os.path.exists(self.figure_dir):
            os.makedirs(self.figure_dir, exist_ok=True)

        self._set_styles()

    @abstractmethod
    def plot(self):
        pass

    def _set_styles(self):
        """可以被子类重写"""
        self.backend_styles = {
            "hybrid": Backend("hybrid", "Hybrid", "tab:blue", "o", 3),
            "pd_disaggregation": Backend("pd", "PD", "tab:orange", "v", 2),
            "chunked_prefill": Backend("cp", "CP", "tab:green", "D", 1),
        }
        plt.rcParams.update({"font.size": 20})

    # 辅助函数

    def _check_success(self, log_dir: str) -> bool:
        log_files = [f for f in os.listdir(log_dir) if f.endswith(".log")]
        for log_file in log_files:
            with open(os.path.join(log_dir, log_file), "r") as file:
                content = file.read()
                if "Traceback" in content:
                    print(f"Error found in {log_file}")
                    return False
        return True

    def _check_result_complete(self, log_dir: str) -> bool:
        log_path = os.path.join(log_dir, "benchmark.log")
        if not os.path.exists(log_path):
            print(f"Lacking file of result: {log_path}")
            return False

        with open(log_path, "r") as file:
            content = file.read()
            if "Full tpots:" not in content:
                print(f"'Lacking data in file: {log_path}")
                return False

        return True

    def _fetch_one_goodput(
        self, qps: str, scheduler: str
    ) -> Tuple[str, List[float], List[float], List[str], float]:
        log_path = os.path.join(
            self.evaluation_dir, qps, scheduler, "logs", "benchmark.log"
        )

        # 提取结果
        with open(log_path, "r") as file:
            content = file.readlines()
            for line in content:
                if "Full ttfts:" in line:
                    full_ttfts = eval(line.split(":")[1])
                if "Full tpots:" in line:
                    full_tpots = eval(line.split(":")[1])
                if "Full api_keys:" in line:
                    full_api_keys = eval(line.split(":")[1])

        # 处理稳态
        assert len(full_ttfts) == len(full_tpots)
        start = int(self.conf.steady_state_start * len(full_ttfts))
        end = int(self.conf.steady_state_end * len(full_ttfts))
        steady_ttfts = full_ttfts[start:end]
        steady_tpots = full_tpots[start:end]
        steady_api_keys = full_api_keys[start:end]

        # 计算满足率
        attained_rate = self._calculate_attainment(
            steady_ttfts, steady_tpots, steady_api_keys, 1.0
        )

        return (scheduler, steady_ttfts, steady_tpots, steady_api_keys, attained_rate)

    def _calculate_attainment(
        self,
        steady_ttfts: List[float],
        steady_tpots: List[float],
        api_keys: List[str],
        scale: float = 1.0,
    ) -> float:
        """计算满足率
        steady_ttfts: 稳态 TTFTs
        steady_tpots: 稳态 TPOTs
        scale: 满足率的比例, 默认为 1.0
        """
        num_attained = sum(
            [
                ttft <= self.conf.slo_dict[api_key][0] * scale
                and tpot <= self.conf.slo_dict[api_key][1] * scale
                for ttft, tpot, api_key in zip(steady_ttfts, steady_tpots, api_keys)
            ]
        )
        attained_rate = num_attained / len(steady_ttfts) * 100

        return attained_rate

    def _fetch_qps_goodputs(
        self, qps: str
    ) -> Optional[List[Tuple[str, List[float], List[float], List[str], float]]]:
        """返回一个 QPS 下所有调度器的结果. 如果有异常或者结果不完整, 返回 None
        返回值格式: list of (scheduler name, steady TTFTs, steady TPOTs, attained_rate)
        """
        qps_dir = os.path.join(self.evaluation_dir, qps)
        for scheduler in self.conf.schedulers:
            log_dir = os.path.join(qps_dir, scheduler, "logs")
            if not self._check_success(log_dir):
                print(f"{scheduler} raised an exception for qps {qps}")
                return None
            if not self._check_result_complete(log_dir):
                print(f"{scheduler}'s results are not complete for qps {qps}")
                return None

        results: List[Tuple[str, List[float], List[float], List[str], float]] = []
        for scheduler in self.conf.schedulers:
            result = self._fetch_one_goodput(qps, scheduler)
            results.append(result)

        return results

    def fetch_qps_scale_attainments(self) -> Optional[List[Tuple[str, List[float]]]]:
        """返回一个 QPS 下所有调度器的 SLO scale. 如果有异常或者结果不完整, 返回 None
        返回值格式: list of (scheduler name, steady TTFTs, steady TPOTs, attained_rate)
        """
        qps = f"{self.conf.slo_scale_qps:.2f}"
        qps_dir = os.path.join(self.evaluation_dir, qps)
        for scheduler in self.conf.schedulers:
            log_dir = os.path.join(qps_dir, scheduler, "logs")
            if not self._check_success(log_dir):
                print(f"{scheduler} raised an exception for qps {qps}")
                return None
            if not self._check_result_complete(log_dir):
                print(f"{scheduler}'s results are not complete for qps {qps}")
                return None

        results: List[Tuple[str, List[float]]] = []
        p90_data: list[tuple[str, float, float]] = []
        for scheduler in self.conf.schedulers:
            result: Tuple[str, List[float], List[float], List[str], float] = (
                self._fetch_one_goodput(qps, scheduler)
            )
            # 计算满足率
            scale_attained_rates = []
            for scale in self.conf.slo_scale_list:
                attained_rate = self._calculate_attainment(
                    result[1], result[2], result[3], scale
                )
                scale_attained_rates.append(attained_rate)
            results.append((scheduler, scale_attained_rates))
            p90_data.append(
                (
                    scheduler,
                    self._get_percentile(result[1], 0.9),
                    self._get_percentile(result[2], 0.9),
                )
            )

        print(results)
        print("cache_p90_data:")
        print(((self.conf.ttft_slo, self.conf.tpot_slo), p90_data))

        return results

    def _get_percentile(self, data: list[float], percentile: float) -> float:
        """
        Get the percentile of the given data.
        """
        if not data:
            return 0.0
        data.sort()
        index = int(percentile * len(data))
        return data[index]

    def _find_intersection(
        self, xs: list[float], ys: list[float], target_y: float
    ) -> Tuple[float, float]:
        """
        Find the intersection point of the given curve (indicated by xs and ys), and
        return the X axis and Y axis of the point.
        """
        for index in range(len(xs) - 1):
            x0 = xs[index]
            x1 = xs[index + 1]
            y0 = ys[index]
            y1 = ys[index + 1]
            if (y0 < target_y) != (y1 < target_y):
                # Intersection point found!
                inter_x = (target_y - y0) * (x1 - x0) / (y1 - y0) + x0
                return (inter_x, target_y)
        print(
            f"WARNING: Intersection point not found! xs: {xs}, ys: {ys}, target_y: {target_y}"
        )
        return (xs[0], target_y)


class AttainmentPlotter(Plotter):
    """满足率绘图类"""

    def __init__(self, conf: EvaluationConf):
        super().__init__(conf)
        self._set_styles()  # 重写格式

        # hardcode 数据
        self.attainment_target: Optional[float] = 90.0

    def _set_styles(self):
        self.backend_styles = {
            "hybrid": Backend("hybrid", "Hybrid", "tab:blue", "o", 3),
            "pd_disaggregation": Backend("pd", "PD", "tab:orange", "v", 2),
            "chunked_prefill": Backend("cp", "CP", "tab:green", "D", 1),
        }
        plt.rcParams.update({"font.size": 20})

    # 满足率绘制
    def plot(self):
        self._plot_all_qps_attainment()

    def _plot_all_qps_attainment(self):
        all_qps_results = []
        for qps in self.conf.qps_list:
            qps_results = self._fetch_qps_goodputs(f"{qps:.2f}")
            if qps_results is None:
                print("[Plot Attainment] Error: results is not complete for qps {qps}")
                return
            all_qps_results.append(qps_results)

        # [[(hy, ..., ..., 100), (pd, ..., ..., 100), (cp, ..., ..., 100)], ...]
        scheduler_results = []
        for scheduler_idx in range(len(all_qps_results[0])):
            scheduler = all_qps_results[0][scheduler_idx][0]
            attained_rates = [
                qps_results[scheduler_idx][4] for qps_results in all_qps_results
            ]
            scheduler_results.append((scheduler, attained_rates))
        print("cached data:")
        print((self.conf.qps_list, scheduler_results))
        self._make_slo_attainment_figure(scheduler_results)

    def _make_slo_attainment_figure(
        self,
        scheduler_results: List[Tuple[str, List[float]]],
    ) -> None:
        fig, ax = plt.subplots(figsize=(6, 3))
        first_intersection_x = -1
        for scheduler, attained_rates in scheduler_results:
            style = self.backend_styles[scheduler]
            ax.plot(
                self.conf.qps_list,
                attained_rates,
                label=style.label,
                color=style.color,
                marker=style.marker,
                zorder=style.zorder,
            )
            if self.attainment_target:
                intersection_x, intersection_y = self._find_intersection(
                    list(map(float, self.conf.qps_list)),
                    attained_rates,
                    self.attainment_target,
                )
                ax.vlines(
                    x=intersection_x,
                    ymin=0,
                    ymax=intersection_y,
                    linestyles="--",
                    colors=style.color,
                )
                ax.axhline(y=self.attainment_target, color="grey", linestyle="--")
                if first_intersection_x == -1:
                    first_intersection_x = intersection_x
                    # Treat the first backend as DistServe
                else:
                    print(
                        f"Improvement ({self.backend_styles["hybrid"].label} compared to {style.label}): {(first_intersection_x/intersection_x - 1) * 100}%"
                    )

        ax.set_xlabel("QPS: " + f"{self.conf.model}, {self.conf.dataset}")
        ax.set_ylabel("SLO Attainment (%)")
        # ax.legend()
        fig.legend(
            frameon=False,
            bbox_to_anchor=(0.95, 1.1, 0, 0),
            ncol=6,
            bbox_transform=plt.gcf().transFigure,
            columnspacing=1,
        )

        ax.set_xlim(min(self.conf.qps_list) * 0.95, max(self.conf.qps_list) * 1.05)

        ax.annotate(
            f"//",
            xy=(min(self.conf.qps_list) * 0.95, -1),
            xytext=(min(self.conf.qps_list) * 0.95, 0),
            textcoords="offset points",
            ha="center",
            va="top",
            fontsize=12,
            color="black",
        )

        fig_path = os.path.join(self.figure_dir, f"slo_attainment.pdf")
        fig.savefig(fig_path)
        print(f"Saved plot to {fig_path}")
        plt.show()


class SLOScalePlotter(Plotter):
    """满足率 scale 绘图类"""

    def __init__(self, conf: EvaluationConf):
        super().__init__(conf)
        self._set_styles()  # 重写格式

        # hardcode 数据
        self.attainment_target: Optional[float] = 90.0

    def _set_styles(self):
        self.backend_styles = {
            "hybrid": Backend("hybrid", "Hybrid", "tab:blue", "o", 3),
            "pd_disaggregation": Backend("pd", "PD", "tab:orange", "v", 2),
            "chunked_prefill": Backend("cp", "CP", "tab:green", "D", 1),
        }
        plt.rcParams.update({"font.size": 20})

    # 满足率绘制
    def plot(self):
        self._plot_scale_attainment()

    def _plot_scale_attainment(self):

        result = self.fetch_qps_scale_attainments()
        if result is None:
            print("[Plot Attainment] Error: results is not complete for qps {qps}")
            return

        self._make_scale_attainment_figure(result)

    def _make_scale_attainment_figure(
        self,
        scheduler_results: List[Tuple[str, List[float]]],
    ) -> None:
        fig, ax = plt.subplots(figsize=(6, 3))
        first_intersection_x = -1
        for scheduler, attained_rates in scheduler_results:
            style = self.backend_styles[scheduler]
            ax.plot(
                self.conf.slo_scale_list,
                attained_rates,
                label=style.label,
                color=style.color,
                marker=style.marker,
                zorder=style.zorder,
            )
            if self.attainment_target:
                intersection_x, intersection_y = self._find_intersection(
                    list(map(float, self.conf.slo_scale_list)),
                    attained_rates,
                    self.attainment_target,
                )
                ax.vlines(
                    x=intersection_x,
                    ymin=0,
                    ymax=intersection_y,
                    linestyles="--",
                    colors=style.color,
                )
                ax.axhline(y=self.attainment_target, color="grey", linestyle="--")
                if first_intersection_x == -1:
                    first_intersection_x = intersection_x
                    # Treat the first backend as DistServe
                else:
                    print(
                        f"Improvement ({self.backend_styles['hybrid'].label} compared to {style.label}): {(first_intersection_x/intersection_x - 1) * 100}%"
                    )

        ax.set_xlabel("SLO Scale: " + f"{self.conf.model}, {self.conf.dataset}")
        ax.set_ylabel("SLO Attainment (%)")
        # ax.legend()
        fig.legend(
            frameon=False,
            bbox_to_anchor=(0.95, 1.1, 0, 0),
            ncol=6,
            bbox_transform=plt.gcf().transFigure,
            columnspacing=1,
        )

        # ax.set_xlim(
        #     max(self.conf.slo_scale_list) * 1.05, min(self.conf.slo_scale_list) * 0.95
        # )
        ax.invert_xaxis()

        ax.annotate(
            f"//",
            xy=(max(self.conf.slo_scale_list) * 1.05, -1),
            xytext=(max(self.conf.slo_scale_list) * 1.05, 0),
            textcoords="offset points",
            ha="center",
            va="top",
            fontsize=12,
            color="black",
        )

        fig_path = os.path.join(self.figure_dir, f"slo_scale.pdf")
        fig.savefig(fig_path)
        print(f"Saved plot to {fig_path}")
        plt.show()


class ScatterPlotter(Plotter):
    """散点图绘图类"""

    def __init__(self, conf: EvaluationConf):
        super().__init__(conf)
        self._set_styles()  # 重写格式

    def _set_styles(self):
        """可以被子类重写"""
        self.backend_styles = {
            "hybrid": Backend("hybrid", "Hybrid", "tab:blue", "o", 3),
            "pd_disaggregation": Backend("pd", "PD", "tab:orange", "v", 2),
            "chunked_prefill": Backend("cp", "CP", "tab:green", "D", 1),
        }
        plt.rcParams.update({"font.size": 20})

    # 散点图绘制
    def plot(self):
        self._plot_all_qps_scatter()

    def _plot_all_qps_scatter(self) -> None:
        """.cache/{model}/{dataset}/{qps}/logs/{scheduler}/benchmark.log"""

        for qps in self.conf.qps_list:
            self._plot_qps_scatter(f"{qps:.2f}")

    def _plot_qps_scatter(self, qps: str) -> None:
        results = self._fetch_qps_goodputs(qps)
        if results is None:
            return

        self._make_scatter_figure(qps, results)

    def _make_scatter_figure(
        self,
        qps: str,
        results: List[Tuple[str, List[float], List[float], List[str], float]],
    ) -> None:
        fig, ax = plt.subplots(figsize=(8, 4))

        # 延迟散点图
        for (
            scheduler,
            steady_ttfts,
            steady_tpots,
            steady_apikeys,
            attained_rate,
        ) in results:
            print("cache_scatter_data:")
            print((scheduler, steady_ttfts, steady_tpots, attained_rate))
            style = self.backend_styles[scheduler]
            ax.scatter(
                steady_tpots,
                steady_ttfts,
                label=f"{style.label}: {attained_rate:.1f}%",
                marker=style.marker,
                color=style.color,
                zorder=style.zorder,
            )

        # SLO 线
        ttft_slo, tpot_slo = self.conf.ttft_slo, self.conf.tpot_slo
        plt.plot([0, tpot_slo], [ttft_slo, ttft_slo], color="r", linestyle="--")
        plt.plot([tpot_slo, tpot_slo], [0, ttft_slo], color="r", linestyle="--")

        # 坐标轴
        ax.set_xlabel(
            "TPOTs" + f"\n(QPS={qps}, {self.conf.model}, {self.conf.dataset})"
        )
        ax.set_ylabel("TTFTs")

        # 图例
        ax.legend(
            frameon=False,
            bbox_to_anchor=(0.95, 1.35, 0, 0),
            facecolor="none",
            edgecolor="none",
            ncol=2,
        )
        # fig.legend(
        #     frameon=False,
        #     bbox_to_anchor=(0.95, 1.1, 0, 0),
        #     ncol=6,
        #     bbox_transform=plt.gcf().transFigure,
        #     columnspacing=1,
        # )

        # 保存
        fig_path = os.path.join(self.figure_dir, f"scatter_{qps}.pdf")
        fig.savefig(fig_path)
        print(f"Saved plot to {fig_path}")
        plt.show()
