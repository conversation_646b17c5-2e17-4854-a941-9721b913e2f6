### 配置数据集负载 ###
# Arxiv
selected_dataset: 4

qps_list: [2.0] # [1.0, 1.25, 1.5, 1.75, 2.0, 2.25]
num_prompts: 512

### 配置指标 ###
steady_state_start: 0
steady_state_end: 1
ttft_slo: 6
tpot_slo: 0.050

slo_scale_qps: 2.0
slo_scale_list: [8, 4, 2, 1, 0.8]

### 配置模型 ###
selected_model: 3
world_size: 2
tp_size: 4

watermark: 0.05
load_aware_buffer_length: 3

slo_dict:
  APIKEY_None: [3, 1]
  APIKEY_Summarization: [6, 0.050]
  APIKEY_Chatbot: [3, 0.110]


### 配置测试对象 ###
schedulers:
  hybrid:
    decoding_size: 1
    # enable_flowing: "" # store_true 类型的参数
    enable_flowing: "--enable_flowing" # store_true 类型的参数
    chunk_sizes: [1024, 1024, 1024, 1024, 128, 128, 128, 128]
    group_sizes: [1, 1]
    flowing_watermark: 0.50
    # schedule_policy: "load_aware"
    schedule_policy: "length_aware"
    # enable_zero_decoding: ""
    enable_zero_decoding: "--enable_zero_decoding"
    prefill_token_limit: [16384, 16384, 16384, 16384, 16384, 16384, 16384, 16384]
    prefill_processing_capacities: [7500, 7500, 7500, 7500, 3000, 3000, 3000, 3000]
    decoding_policy: "simple_flowing"
    # decoding_policy: "prophet_flowing"
    disable_async_output: [True, True, True, True, False, False, False, False] # Pure P = True
  pd_disaggregation:
    decoding_size: 1
    chunk_sizes: [2048, 2048, 2048, 2048, 2048, 2048, 2048, 2048]
    group_sizes: []
    enable_flowing: "" # store_true 类型的参数
    flowing_watermark: 0.05
    schedule_policy: "load_aware"
    enable_zero_decoding: ""
    prefill_token_limit: [0]
    prefill_processing_capacities: [0]
    decoding_policy: "simple_flowing"
    disable_async_output: [True, True, True, True, True, True, True, True] # Pure P = True
  chunked_prefill:
    decoding_size: 0
    chunk_sizes: [1024, 1024, 1024, 1024, 1024, 1024, 1024, 1024]
    group_sizes: []
    enable_flowing: "" # store_true 类型的参数
    flowing_watermark: 0.05
    schedule_policy: "load_aware"
    enable_zero_decoding: ""
    prefill_token_limit: [0]
    prefill_processing_capacities: [0]
    decoding_policy: "simple_flowing"
    disable_async_output: [False, False, False, False, False, False, False, False] # Pure P = True

### 平台特殊配置 (高优先级) ###
platforms: 
  "cwang-X99": # X99 双卡 3060 平台
    qps_list: [3]
    num_prompts: 32
    selected_model: 0
    world_size: 2

  "992cdc39c3cc": # 5 卡 4090 平台
    qps_list: [3]
    num_prompts: 32
    selected_model: 0
