# 🔍 CUDA Memory Leak Debugging Guide

## 问题现状

你已经确认了tensor reference counting修复成功（`use_count: 1`），但GPU内存泄露仍然存在。这表明问题不在于tensor对象的引用计数，而在于底层的CUDA内存管理。

## 🎯 新增的调试功能

### 1. **C++ CUDA内存监控**

在 `kv_buffer.hpp` 中添加了精确的CUDA内存监控：

#### **A. Tensor销毁前的内存检查**
```cpp
// DEBUG: Check CUDA memory before tensor destruction
size_t free_before, total;
cudaMemGetInfo(&free_before, &total);
printf("[CUDA_DEBUG] BEFORE TENSOR DESTRUCTION - Request: %s, Free CUDA memory: %.1fMB\n",
       send_task.request_id.c_str(), free_before / (1024.0 * 1024.0));
```

#### **B. Tensor销毁后的内存检查**
```cpp
// Helper method to check CUDA memory after tensor destruction
void debug_check_cuda_memory_after_task(const std::string& request_id) {
    size_t free_after, total;
    cudaMemGetInfo(&free_after, &total);
    printf("[CUDA_DEBUG] AFTER TENSOR DESTRUCTION - Request: %s, Free CUDA memory: %.1fMB\n",
           request_id.c_str(), free_after / (1024.0 * 1024.0));
}
```

### 2. **Python精确内存监控工具**

#### **A. `CUDAMemoryMonitor` 类**
- **双重监控**: 同时使用PyTorch和nvidia-smi获取内存信息
- **操作级追踪**: 记录每个操作前后的内存变化
- **泄露识别**: 自动识别导致内存增长的具体操作

#### **B. `cuda_memory_leak_pinpoint.py` 脚本**
- **逐步监控**: 在KV传输管道的每个步骤监控内存
- **泄露定位**: 精确识别哪个操作导致内存泄露
- **对比分析**: 比较PyTorch和NVIDIA的内存使用情况

## 🚀 如何使用调试工具

### **步骤1: 重建C++扩展**
```bash
chmod +x rebuild_and_test_cuda_leak.sh
./rebuild_and_test_cuda_leak.sh
```

### **步骤2: 分析调试输出**

#### **A. 查看CUDA内存变化**
在日志中寻找这些模式：
```
[CUDA_DEBUG] BEFORE TENSOR DESTRUCTION - Request: test_req_0, Free CUDA memory: 15234.5MB
[CUDA_DEBUG] AFTER TENSOR DESTRUCTION - Request: test_req_0, Free CUDA memory: 15234.5MB
```

**正常情况**: `AFTER` 的可用内存应该比 `BEFORE` 更多（tensor被释放）
**泄露情况**: `AFTER` 的可用内存没有增加（tensor内存未释放）

#### **B. 查看内存增长操作**
```
🚨 MEMORY LEAK OPERATIONS (5 found):
  1. before_add_task_0 → after_add_task_0
     PyTorch: +256.0MB, NVIDIA: +256.0MB
  2. after_add_task_0 → after_processing_0
     PyTorch: +0.0MB, NVIDIA: +0.0MB
```

这会告诉你**具体哪个操作**导致了内存泄露。

## 🔍 可能的泄露原因和解决方案

### **1. CUDA Stream同步问题**
**症状**: PyTorch显示tensor已释放，但NVIDIA显示内存未释放
**原因**: CUDA操作是异步的，tensor可能在CUDA stream中仍被使用
**解决方案**: 
```cpp
// 在tensor销毁前确保CUDA操作完成
cudaStreamSynchronize(stream);
// 或者
cudaDeviceSynchronize();
```

### **2. CUDA Context缓存**
**症状**: 内存在多次操作后累积，但单次操作看起来正常
**原因**: CUDA context缓存了内存分配
**解决方案**:
```cpp
// 强制清理CUDA缓存
torch::cuda::empty_cache();
```

### **3. Tensor存储共享**
**症状**: 不同tensor对象共享相同的CUDA内存
**原因**: PyTorch的tensor可能共享底层存储
**解决方案**: 检查tensor的`storage().data_ptr()`是否相同

### **4. CUDA内存池问题**
**症状**: 内存分配器没有真正释放内存给系统
**原因**: CUDA内存池为了性能保留已分配的内存
**解决方案**: 使用`cudaMemGetInfo`而不是PyTorch的内存统计

## 📊 预期的调试结果

### **如果是CUDA Stream同步问题**:
```
[CUDA_DEBUG] BEFORE TENSOR DESTRUCTION - Free: 15000MB
[CUDA_DEBUG] AFTER TENSOR DESTRUCTION - Free: 15000MB  # 没有变化
```

### **如果是内存池缓存问题**:
```
PyTorch Allocated: +0.0MB  # PyTorch认为已释放
NVIDIA Used: +256.0MB      # 但NVIDIA显示仍在使用
```

### **如果是真正的内存泄露**:
```
PyTorch Allocated: +256.0MB  # PyTorch也显示内存增长
NVIDIA Used: +256.0MB        # NVIDIA确认内存增长
```

## 🎯 下一步行动计划

### **1. 运行调试工具**
```bash
./rebuild_and_test_cuda_leak.sh
```

### **2. 分析输出结果**
- 查看 `[CUDA_DEBUG]` 输出，确认tensor销毁前后的内存变化
- 查看 `MEMORY LEAK OPERATIONS`，识别具体的泄露操作
- 比较PyTorch和NVIDIA的内存使用差异

### **3. 根据结果采取行动**

#### **如果是同步问题**:
在tensor销毁前添加 `cudaStreamSynchronize()` 或 `cudaDeviceSynchronize()`

#### **如果是缓存问题**:
定期调用 `torch::cuda::empty_cache()` 清理缓存

#### **如果是真正的泄露**:
检查C++代码中是否有tensor被意外保存或引用

## 🔧 可能需要的修复

基于调试结果，可能需要在以下位置添加修复：

### **1. 在send操作后强制同步**
```cpp
// 在 execute_send_task 结束前
cudaStreamSynchronize(stream);  // 确保CUDA操作完成
```

### **2. 定期清理CUDA缓存**
```cpp
// 在后台线程中定期执行
if (task_count % 10 == 0) {
    torch::cuda::empty_cache();
}
```

### **3. 检查tensor存储共享**
```cpp
// 调试tensor存储
printf("Tensor storage ptr: %p\n", tensor.storage().data_ptr());
```

通过这些调试工具，我们应该能够精确定位GPU内存泄露的具体位置和原因，然后实施针对性的修复。
