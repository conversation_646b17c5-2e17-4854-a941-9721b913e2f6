#!/usr/bin/env python3
"""
GPU Memory Leak Detector for KV Transfer System

This tool helps identify exactly where GPU memory is being leaked by:
1. Tracking CUDA memory allocations at granular level
2. Monitoring memory usage around specific operations
3. Identifying memory that's allocated but never freed
4. Providing detailed memory allocation traces
"""

import torch
import gc
import time
import threading
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class MemorySnapshot:
    timestamp: float
    context: str
    allocated_mb: float
    reserved_mb: float
    free_mb: float
    active_tensors: int


class GPUMemoryTracker:
    """Advanced GPU memory tracking with allocation-level granularity."""
    
    def __init__(self, device_id: int = 0):
        self.device_id = device_id
        self.device = torch.device(f"cuda:{device_id}")
        self.snapshots: List[MemorySnapshot] = []
        self.baseline: Optional[MemorySnapshot] = None
        
    def get_memory_info(self) -> Tuple[float, float, float]:
        """Get detailed GPU memory information."""
        if not torch.cuda.is_available():
            return 0.0, 0.0, 0.0
            
        allocated = torch.cuda.memory_allocated(self.device) / 1024**2
        reserved = torch.cuda.memory_reserved(self.device) / 1024**2
        
        # Get total GPU memory
        props = torch.cuda.get_device_properties(self.device)
        total = props.total_memory / 1024**2
        free = total - reserved
        
        return allocated, reserved, free
    
    def count_active_tensors(self) -> int:
        """Count active CUDA tensors (approximation)."""
        # Force garbage collection to get accurate count
        gc.collect()
        
        # Count objects that look like CUDA tensors
        tensor_count = 0
        for obj in gc.get_objects():
            if isinstance(obj, torch.Tensor) and obj.is_cuda:
                tensor_count += 1
        
        return tensor_count
    
    def take_snapshot(self, context: str) -> MemorySnapshot:
        """Take a detailed memory snapshot."""
        allocated, reserved, free = self.get_memory_info()
        active_tensors = self.count_active_tensors()
        
        snapshot = MemorySnapshot(
            timestamp=time.time(),
            context=context,
            allocated_mb=allocated,
            reserved_mb=reserved,
            free_mb=free,
            active_tensors=active_tensors
        )
        
        self.snapshots.append(snapshot)
        return snapshot
    
    def set_baseline(self, context: str = "baseline"):
        """Set baseline memory usage."""
        self.baseline = self.take_snapshot(context)
        print(f"[GPU_TRACKER] Baseline set: {self.baseline.allocated_mb:.1f}MB allocated, "
              f"{self.baseline.active_tensors} active tensors")
    
    def analyze_leak(self) -> Dict:
        """Analyze memory usage patterns to identify leaks."""
        if len(self.snapshots) < 2:
            return {"error": "Need at least 2 snapshots"}
        
        first = self.snapshots[0]
        last = self.snapshots[-1]
        
        allocated_growth = last.allocated_mb - first.allocated_mb
        reserved_growth = last.reserved_mb - first.reserved_mb
        tensor_growth = last.active_tensors - first.active_tensors
        
        # Find peak memory usage
        peak_allocated = max(s.allocated_mb for s in self.snapshots)
        peak_reserved = max(s.reserved_mb for s in self.snapshots)
        
        # Identify contexts with highest memory usage
        context_memory = {}
        for snapshot in self.snapshots:
            if snapshot.context not in context_memory:
                context_memory[snapshot.context] = []
            context_memory[snapshot.context].append(snapshot.allocated_mb)
        
        context_stats = {}
        for context, memories in context_memory.items():
            context_stats[context] = {
                'count': len(memories),
                'avg_mb': sum(memories) / len(memories),
                'max_mb': max(memories),
                'min_mb': min(memories)
            }
        
        return {
            'duration_seconds': last.timestamp - first.timestamp,
            'allocated_growth_mb': allocated_growth,
            'reserved_growth_mb': reserved_growth,
            'tensor_count_growth': tensor_growth,
            'peak_allocated_mb': peak_allocated,
            'peak_reserved_mb': peak_reserved,
            'context_analysis': context_stats,
            'leak_detected': allocated_growth > 50 or tensor_growth > 10  # Thresholds
        }
    
    def print_analysis(self):
        """Print detailed memory analysis."""
        analysis = self.analyze_leak()
        
        if "error" in analysis:
            print(f"❌ Analysis error: {analysis['error']}")
            return
        
        print("\n" + "="*60)
        print("🔍 GPU MEMORY LEAK ANALYSIS")
        print("="*60)
        
        print(f"📊 Memory Growth Analysis:")
        print(f"  Allocated Memory Growth: {analysis['allocated_growth_mb']:.1f}MB")
        print(f"  Reserved Memory Growth: {analysis['reserved_growth_mb']:.1f}MB")
        print(f"  Active Tensor Count Growth: {analysis['tensor_count_growth']}")
        print(f"  Peak Allocated Memory: {analysis['peak_allocated_mb']:.1f}MB")
        print(f"  Peak Reserved Memory: {analysis['peak_reserved_mb']:.1f}MB")
        print(f"  Analysis Duration: {analysis['duration_seconds']:.1f}s")
        
        if analysis['leak_detected']:
            print(f"\n🚨 MEMORY LEAK DETECTED!")
            if analysis['allocated_growth_mb'] > 50:
                print(f"  - Allocated memory grew by {analysis['allocated_growth_mb']:.1f}MB")
            if analysis['tensor_count_growth'] > 10:
                print(f"  - Active tensor count grew by {analysis['tensor_count_growth']}")
        else:
            print(f"\n✅ No significant memory leak detected")
        
        print(f"\n📈 Context-Specific Memory Usage:")
        sorted_contexts = sorted(
            analysis['context_analysis'].items(), 
            key=lambda x: x[1]['max_mb'], 
            reverse=True
        )
        
        for context, stats in sorted_contexts[:10]:
            print(f"  {context}:")
            print(f"    Max: {stats['max_mb']:.1f}MB, Avg: {stats['avg_mb']:.1f}MB, Count: {stats['count']}")


def test_kv_operations_memory_leak():
    """Test KV operations for GPU memory leaks with detailed tracking."""
    print("🔍 Testing KV Operations for GPU Memory Leaks")
    print("=" * 50)
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return False
    
    tracker = GPUMemoryTracker()
    tracker.set_baseline("test_start")
    
    try:
        import kv_buffer
        
        # Create KV buffer
        tracker.take_snapshot("before_buffer_creation")
        
        buffer = kv_buffer.KvBuffer(
            host="localhost",
            port=12345,
            rank=0,
            size=1,
            tp_rank=0,
            tp_size=1,
            device=0,
            send_buffer_size_threshold=1024*1024*100,
            recv_buffer_size_threshold=1024*1024*100
        )
        
        tracker.take_snapshot("after_buffer_creation")
        
        # Create test tensor
        tensor_shape = (2, 32, 512, 32, 128)  # Smaller for testing
        test_tensor = torch.randn(tensor_shape, device='cuda', dtype=torch.float16)
        tensor_size_mb = test_tensor.numel() * test_tensor.element_size() / 1024**2
        
        tracker.take_snapshot("after_tensor_creation")
        print(f"📦 Created test tensor: {tensor_size_mb:.1f}MB")
        
        # Test multiple operations
        num_operations = 20
        print(f"🔄 Testing {num_operations} KV operations...")
        
        for i in range(num_operations):
            request_id = f"test_req_{i}"
            
            # Take snapshot before operation
            tracker.take_snapshot(f"before_op_{i}")
            
            # Add send task
            buffer.add_send_task(request_id, 0, test_tensor)
            
            # Take snapshot after add_send_task
            tracker.take_snapshot(f"after_add_task_{i}")
            
            # Wait for processing
            time.sleep(0.5)
            
            # Take snapshot after processing
            tracker.take_snapshot(f"after_processing_{i}")
            
            # Force cleanup every few operations
            if i % 5 == 0:
                gc.collect()
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                tracker.take_snapshot(f"after_cleanup_{i}")
            
            if i % 5 == 0:
                current = tracker.snapshots[-1]
                baseline = tracker.baseline
                growth = current.allocated_mb - baseline.allocated_mb
                print(f"  Operation {i}: Memory growth = {growth:.1f}MB, "
                      f"Active tensors = {current.active_tensors}")
        
        # Final cleanup
        del test_tensor
        buffer.close()
        
        tracker.take_snapshot("after_final_cleanup")
        
        # Force aggressive cleanup
        gc.collect()
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        
        tracker.take_snapshot("after_aggressive_cleanup")
        
        # Analyze results
        tracker.print_analysis()
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        tracker.take_snapshot("error_state")
        tracker.print_analysis()
        return False


def monitor_cuda_memory_detailed():
    """Monitor CUDA memory with detailed allocation tracking."""
    print("🔍 Detailed CUDA Memory Monitoring")
    print("=" * 40)
    
    if not torch.cuda.is_available():
        print("❌ CUDA not available")
        return
    
    # Enable memory debugging
    torch.cuda.memory._record_memory_history(True, trace_alloc_max_entries=100000)
    
    try:
        # Run the KV operations test
        test_kv_operations_memory_leak()
        
        # Get memory snapshot
        print("\n📊 CUDA Memory Allocation Snapshot:")
        snapshot = torch.cuda.memory._snapshot()
        
        # Analyze allocations
        total_allocated = 0
        active_allocations = 0
        
        for trace in snapshot:
            if trace['action'] == 'alloc' and trace['active']:
                total_allocated += trace['size']
                active_allocations += 1
        
        print(f"  Active allocations: {active_allocations}")
        print(f"  Total allocated: {total_allocated / 1024**2:.1f}MB")
        
        # Find largest allocations
        large_allocs = [
            trace for trace in snapshot 
            if trace['action'] == 'alloc' and trace['active'] and trace['size'] > 1024*1024  # > 1MB
        ]
        
        print(f"\n🔍 Large Active Allocations (>1MB):")
        for i, alloc in enumerate(sorted(large_allocs, key=lambda x: x['size'], reverse=True)[:10]):
            size_mb = alloc['size'] / 1024**2
            print(f"  {i+1}. {size_mb:.1f}MB - Address: {alloc['addr']}")
            if 'frames' in alloc:
                print(f"     Stack trace: {alloc['frames'][:2]}")  # Show first 2 frames
        
    finally:
        # Disable memory debugging
        torch.cuda.memory._record_memory_history(False)


if __name__ == "__main__":
    print("🚀 GPU Memory Leak Detection Tool")
    print("=" * 50)
    
    # Run detailed monitoring
    monitor_cuda_memory_detailed()
